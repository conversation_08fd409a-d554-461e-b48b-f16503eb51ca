package cn.jwis.product.pdm.customer.service.release;

import cn.jwis.platform.plm.workflow.engine.order.entity.ProcessOrder;
import cn.jwis.product.pdm.customer.entity.IntegrationRecord;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Data
@Component
@Slf4j
public class SMISRelease extends EntityRelease{
    @Override
    public IntegrationRecord release(ProcessOrder processOrder, String entityOid, String recordOid, int isFirst) {
        return null;
    }

    @Override
    public void afterPropertiesSet() throws Exception {

    }
}
