package cn.jwis.product.pdm.customer.service.delegate;

import cn.hutool.json.JSONUtil;
import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.util.SpringContextUtil;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.platform.plm.foundation.common.service.InstanceHelper;
import cn.jwis.product.pdm.change.entity.ECA;
import cn.jwis.product.pdm.change.response.ChangeInfo;
import cn.jwis.product.pdm.change.service.ECAService;
import cn.jwis.product.pdm.change.service.ECAServiceImpl;
import cn.jwis.product.pdm.customer.entity.IntegrationRecord;
import cn.jwis.product.pdm.customer.repo.CustomerCommonRepo;
import cn.jwis.product.pdm.customer.repo.IntegrationMonitorRepo;
import cn.jwis.product.pdm.customer.service.release.EntityRelease;
import cn.jwis.product.pdm.customer.service.release.EntityReleaseFactory;
import cn.jwis.product.pdm.customer.service.release.PartEntityRelease;
import cn.jwis.product.pdm.partbom.part.service.PartHelper;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.util.Pair;
import org.hsqldb.lib.HashSet;
import org.hsqldb.lib.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BatchSendERP {

    @Autowired
    ECAService ecaService;

    @Autowired
    CustomerCommonRepo customerCommonRepo;

    @Autowired
    IntegrationMonitorRepo monitorRepo;


    public String batchSendERP(List<InstanceEntity> originInstanceEntityList, Boolean checkPermission) {
        UserDTO currUser = SessionHelper.getCurrentUser();
        if(checkPermission && currUser == null || (checkPermission && !"sys_admin".equals(currUser.getAccount()) && !customerCommonRepo.batchSendERPPermission(currUser.getAccount())))
            throw new RuntimeException("只有【组织管理员】和【数据管理员】才能批量发送ERP!");

//        List<Map> instanceList = customerCommonRepo.findByOidList(originInstanceEntityList.stream().map(it -> it.getOid()).collect(Collectors.toList()));
//        StringBuilder stringBuilder = new StringBuilder();
//        List<Map> unReleasList = instanceList.stream().filter(it -> {
//            if(!"Released".equals(it.get("lifecycleStatus"))){
//                stringBuilder.append(" 名称：" + it.getOrDefault("name", "") + " 编号：" + it.getOrDefault("number", "") + "的数据不是已发布状态");
//                return Boolean.TRUE;
//            }
//            return Boolean.FALSE;
//        }).collect(Collectors.toList());
//        if(stringBuilder.length() > 0)
//            throw new RuntimeException(stringBuilder.toString());

        try {
            ECAServiceImpl ecaService = (ECAServiceImpl)SpringContextUtil.getBean("ECAServiceImpl");
            // instanceEntityList：评审对象集合（部件，文档等）
            List<InstanceEntity> instanceEntityList = new ArrayList<>();
            for(InstanceEntity instanceEntity : originInstanceEntityList){
                if(ECA.TYPE.equals(instanceEntity.getType())){
                    InstanceHelper instanceHelper = (InstanceHelper) SpringContextUtil.getBean("instanceHelperImpl");
                    List<ChangeInfo> list = ecaService.findChangeInfo(instanceEntity.getOid(),new ArrayList<>());
                    list.stream().forEach(changeInfo -> {
                        JSONObject jsonObject = instanceHelper.findLatest(changeInfo.getOid(), changeInfo.getType());
                        InstanceEntity newEntity = jsonObject.toJavaObject(InstanceEntity.class);
                        instanceEntityList.add(newEntity);
                    });
                }else {
                    instanceEntityList.add(instanceEntity);
                }
            }
            // 评审对象为空则不处理
            if(CollectionUtil.isEmpty(instanceEntityList)){
                log.info("EntityReleaseDelegate  instanceEntity is empty! ");
                return "EntityReleaseDelegate  instanceEntity is empty! ";
            }
            // 发放数据,返回结果为此次需要发布的数据
            String unReleaseData = releaseData(instanceEntityList);
            log.info("EntityReleaseDelegate instanceEntityList=" + instanceEntityList.stream().map(it -> it.getNumber()).collect(Collectors.joining(",")));
            log.info("EntityReleaseDelegate release=" + unReleaseData);
        } catch (Exception e) {
            log.error("EntityReleaseDelegate execute error==>", e);
            StringBuffer buffer = new StringBuffer();
            StackTraceElement[] stackTrace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : stackTrace) {
                buffer.append(stackTraceElement.toString() + "\n");
            }
            throw new RuntimeException(e.getMessage());
//            return "EntityReleaseDelegate execute error==>" + e.getMessage();
        }
        return "EntityReleaseDelegate success";
    }

    public void batchSendERPWithBusinessType(List<InstanceEntity> originInstanceEntityList, Boolean checkPermission, EntityReleaseFactory.BusinessType... types) {
        // 转换成原来需要的逗号分隔字符串
        String typeStr = Arrays.stream(types)
                .map(Enum::name)
                .collect(Collectors.joining(","));

        UserDTO currUser = SessionHelper.getCurrentUser();
        if(checkPermission && currUser == null || (checkPermission && !"sys_admin".equals(currUser.getAccount()) && !customerCommonRepo.batchSendERPPermission(currUser.getAccount())))
            throw new RuntimeException("只有【组织管理员】和【数据管理员】才能批量发送ERP!");

        try {
            ECAServiceImpl ecaService = (ECAServiceImpl)SpringContextUtil.getBean("ECAServiceImpl");
            // instanceEntityList：评审对象集合（部件，文档等）
            List<InstanceEntity> instanceEntityList = new ArrayList<>();
            for(InstanceEntity instanceEntity : originInstanceEntityList){
                if(ECA.TYPE.equals(instanceEntity.getType())){
                    InstanceHelper instanceHelper = (InstanceHelper) SpringContextUtil.getBean("instanceHelperImpl");
                    List<ChangeInfo> list = ecaService.findChangeInfo(instanceEntity.getOid(),new ArrayList<>());
                    list.stream().forEach(changeInfo -> {
                        JSONObject jsonObject = instanceHelper.findLatest(changeInfo.getOid(), changeInfo.getType());
                        InstanceEntity newEntity = jsonObject.toJavaObject(InstanceEntity.class);
                        instanceEntityList.add(newEntity);
                    });
                }else {
                    instanceEntityList.add(instanceEntity);
                }
            }
            // 评审对象为空则不处理
            if(CollectionUtil.isEmpty(instanceEntityList)){
                log.info("EntityReleaseDelegate  instanceEntity is empty! ");
                return;
            }
            // 发放数据,返回结果为此次需要发布的数据
            String unReleaseData = releaseDataWithBusinessType(instanceEntityList,typeStr);
            log.info("EntityReleaseDelegate instanceEntityList=" + instanceEntityList.stream().map(it -> it.getNumber()).collect(Collectors.joining(",")));
            log.info("EntityReleaseDelegate release=" + unReleaseData);
        } catch (Exception e) {
            log.error("EntityReleaseDelegate execute error", e);
            throw new RuntimeException("批量发送ERP失败", e);
        }
    }

    private String releaseDataWithBusinessType(List<InstanceEntity> instanceEntityList,String businessTypes) {
        List<String> needPublish = new ArrayList<>(); // 需要发布的
        // 排序，非筛选或未成型物料排在前面发布
        List<InstanceEntity> sortedEntity = sortEntity(instanceEntityList);
        // 检查 sortedEntity 中是否有任何一个对象的 number 属性为 null
        boolean anyNumberNull = sortedEntity.stream().anyMatch(entity -> entity.getNumber() == null);
        if (!anyNumberNull) {
            sortedEntity = filterByUniqueNumber(sortedEntity);
        }

        StringBuffer result = new StringBuffer();
//        String businessTypes = "item,bom,doc,mcad,ecad";
        for (String businessType : businessTypes.split(",")) {
            sortedEntity.stream().forEach(instanceEntity -> {
                // 根据流程类型或者评审对象类型找到对应的发布能力提供者
                EntityRelease entityRelease = EntityReleaseFactory.match(businessType);
                //全部发布
                //如果是不属于该businessType的数据，会返回null，不处理
                if (entityRelease != null) {
                    try {
                        IntegrationRecord record = entityRelease.release(null, instanceEntity.getOid(), "", 0);
                        if (record != null) {
                            result.append(record.getBusinessOid()).append(",");
                            needPublish.add(instanceEntity.getOid());
                        }

                        Thread.sleep(2);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.warn("发布过程中被中断，businessType: {}, oid: {}", businessType, instanceEntity.getOid(), e);
                    } catch (Exception e) {
                        log.error("发布失败，businessType: {}, oid: {}", businessType, instanceEntity.getOid(), e);
                    }
                }
            });
        }
        // 未发布的数据在此处设置为已发放状态，已发布的数据在回调函数中设置为已发放状态
        List<InstanceEntity> needUpdateStatus = instanceEntityList.stream().filter(
                i->!needPublish.contains(i.getOid())).collect(Collectors.toList());

        log.info("releaseData-needUpdateStatus--->>>>{}", JSONUtil.toJsonStr(needUpdateStatus));
        updateStatus(needUpdateStatus);
        return StringUtil.isBlank(result.toString()) ? "none" : result.toString() ;
    }
    private String releaseData(List<InstanceEntity> instanceEntityList) {
        List<String> needPublish = new ArrayList<>(); // 需要发布的
        // 排序，非筛选或未成型物料排在前面发布
        List<InstanceEntity> sortedEntity = sortEntity(instanceEntityList);
        // 检查 sortedEntity 中是否有任何一个对象的 number 属性为 null
        boolean anyNumberNull = sortedEntity.stream().anyMatch(entity -> entity.getNumber() == null);
        if (!anyNumberNull) {
            sortedEntity = filterByUniqueNumber(sortedEntity);
        }

        StringBuffer result = new StringBuffer();
        String businessTypes = "item,bom,doc,mcad,ecad";
        for (String businessType : businessTypes.split(",")) {
            sortedEntity.stream().forEach(instanceEntity -> {
                // 根据流程类型或者评审对象类型找到对应的发布能力提供者
                EntityRelease entityRelease = EntityReleaseFactory.match(businessType);
                //全部发布
                //如果是不属于该businessType的数据，会返回null，不处理
                if (entityRelease != null) {
                    try {
                        IntegrationRecord record = entityRelease.release(null, instanceEntity.getOid(), "", 0);
                        if (record != null) {
                            result.append(record.getBusinessOid()).append(",");
                            needPublish.add(instanceEntity.getOid());
                        }

                        Thread.sleep(2);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.warn("发布过程中被中断，businessType: {}, oid: {}", businessType, instanceEntity.getOid(), e);
                    } catch (Exception e) {
                        log.error("发布失败，businessType: {}, oid: {}", businessType, instanceEntity.getOid(), e);
                    }
                }
            });
        }
        // 未发布的数据在此处设置为已发放状态，已发布的数据在回调函数中设置为已发放状态
        List<InstanceEntity> needUpdateStatus = instanceEntityList.stream().filter(
                i->!needPublish.contains(i.getOid())).collect(Collectors.toList());

        log.info("releaseData-needUpdateStatus--->>>>{}", JSONUtil.toJsonStr(needUpdateStatus));
        updateStatus(needUpdateStatus);
        return StringUtil.isBlank(result.toString()) ? "none" : result.toString() ;
    }

    private void updateStatus(List<InstanceEntity> needUpdateStatus) {
        if(CollectionUtil.isEmpty(needUpdateStatus)){
            return;
        }
        IntegrationMonitorRepo monitorRepo = (IntegrationMonitorRepo) SpringContextUtil.getBean("integrationMonitorNeo4jRepoImpl");
        String lifeCycleStatus = "Released";
        for(InstanceEntity instanceEntity : needUpdateStatus) {
            monitorRepo.updateStatusByOid(instanceEntity.getOid(), instanceEntity.getType(), lifeCycleStatus);
        }
    }

    @Value("${part.autoScreen:false}")
    private Boolean autoScreen;

    @Autowired
    PartHelper partHelper;

    @Autowired
    PartEntityRelease partEntityRelease;

    private List<InstanceEntity> sortEntity(List<InstanceEntity> instanceEntityList) {
        List<InstanceEntity> first = new ArrayList<>();
        List<InstanceEntity> second = new ArrayList<>();

        if (autoScreen) {
            List<InstanceEntity> thirdList = instanceEntityList.stream().map(instance -> {
                if ("PartIteration".equals(instance.getType())) {
                    return partEntityRelease.findScreenFormingPart(instance.getOid()).stream();
                }
                return null;
            }).filter(Objects::nonNull).flatMap(it -> it).collect(Collectors.toList());
            instanceEntityList.addAll(thirdList);
        }

        for(InstanceEntity entity : instanceEntityList){
            Pair<List<InstanceEntity>,Boolean> pair = partEntityRelease.findScreenOrFormingPart(entity.getOid());
            if(pair == null) {
                first.add(entity);
                continue;
            }
            List<InstanceEntity> screenList = pair.getFirst();
            if(CollectionUtil.isEmpty(screenList)) {
                first.add(entity);
            } else {
                second.add(entity);
            }
        }
        first.addAll(second);
        return first;
    }


    /**
     * 只保留一个 number 相同的 InstanceEntity
     * @param instanceEntityList
     * @return
     */
    public List<InstanceEntity> filterByUniqueNumber(List<InstanceEntity> instanceEntityList) {
        //1、先针对流水号做一次过滤
        Set<String> seenNumbers = new HashSet<>();
        // 过滤出number唯一的InstanceEntity
        List<InstanceEntity> collect = instanceEntityList.stream()
                .filter(entity -> entity.getNumber() != null && seenNumbers.add(entity.getNumber()))
                .collect(Collectors.toList());
        //2、再根据是否发送成功做一次过滤
        List<InstanceEntity> result = new ArrayList<>();
       /* for (InstanceEntity instanceEntity : collect) {
            //增加版本
            if (null == instanceEntity.getVersion() && "PartIteration".equals(instanceEntity.getType())) {
                //为当前实例查询详情
                PartIteration byOid = partHelper.findByOid(instanceEntity.getOid());
                instanceEntity.setVersion(null != byOid ? byOid.getVersion() : "");
            }
            IntegrationRecord recordByNumber = monitorRepo.findRecordByNumber(instanceEntity);
            if (recordByNumber == null) {
                // 如果 recordByNumber 为 null，直接添加 instanceEntity 到 result
                result.add(instanceEntity);
            } else if (!recordByNumber.getIsSuccess()) {
                String msg = recordByNumber.getMsg();
                if (!(StrUtil.isNotEmpty(msg) && (msg.equals("waiting") || msg.contains("已存在")))) {
                    result.add(instanceEntity);
                }
            }
        }*/
        //不对是否发送成功过滤
        result.addAll(collect);
        return result;
    }

}
