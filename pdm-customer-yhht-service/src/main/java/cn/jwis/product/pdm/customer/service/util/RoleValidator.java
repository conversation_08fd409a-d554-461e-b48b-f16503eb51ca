package cn.jwis.product.pdm.customer.service.util;

import cn.jwis.framework.base.exception.JWIException;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

public class RoleValidator {

    /**
     * 校验列表中是否包含指定角色（用户信息非空）
     *
     * @param list          数据列表
     * @param roleExtractor 从对象提取角色名的方法
     * @param validChecker  判断该对象是否有效（如用户列表不为空）
     * @param roleErrorMap  角色 -> 错误提示
     * @param <T>           列表元素类型
     */
    public static <T> void validateRoles(
            List<T> list,
            Function<T, String> roleExtractor,
            Predicate<T> validChecker,
            Map<String, String> roleErrorMap) {

        if (list == null || list.isEmpty()) {
            return; // 或者根据业务需要直接抛异常
        }

        Set<String> existingRoles = list.stream()
                .filter(Objects::nonNull)
                .filter(validChecker)
                .map(roleExtractor)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        roleErrorMap.forEach((role, errorMsg) -> {
            if (!existingRoles.contains(role)) {
                throw new JWIException(errorMsg);
            }
        });
    }
}