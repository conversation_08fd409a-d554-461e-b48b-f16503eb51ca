package cn.jwis.product.pdm.customer.service.dto;

import lombok.Data;
import java.util.List;

@Data
public class SMISWorkdefinitionResponse {
    private String itemcode; // 新增
    private List<String> workdefinitions; //工艺路线版本
    private List<WorkRequest> workrequests; //工单列表
}

@Data
 class WorkRequest {
    private String workrequestno;   // 工单编号
    private String satellitenumber; // 整星描述
    private String qty;             // 计划数量
    private String mainorg;         // 主制组织
    private String workdefinition;  // 工艺路线
    private String sate;            // 工单状态
}