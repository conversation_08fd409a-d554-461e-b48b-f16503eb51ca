package cn.jwis.product.pdm.customer.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.jwis.framework.base.domain.able.ModelAble;
import cn.jwis.framework.base.domain.entity.BaseEntity;
import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.exception.JWIServiceException;
import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.platform.plm.container.entity.Container;
import cn.jwis.platform.plm.container.entity.Folder;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.foundation.attributerule.entity.AttributeRule;
import cn.jwis.platform.plm.foundation.attributerule.entity.valueObj.AttributeSegment;
import cn.jwis.platform.plm.foundation.attributerule.service.AttributeRuleHelper;
import cn.jwis.platform.plm.foundation.classification.able.ClassificationAble;
import cn.jwis.platform.plm.foundation.classification.entity.Classification;
import cn.jwis.platform.plm.foundation.classification.responce.ClsPropertyWithRel;
import cn.jwis.platform.plm.foundation.classification.service.ClassificationPropertyHelper;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelperImpl;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityService;
import cn.jwis.platform.plm.foundation.common.service.InstanceHelper;
import cn.jwis.platform.plm.foundation.common.utils.ReflectUtils;
import cn.jwis.platform.plm.foundation.extendedproperty.able.ExtendedPropertyAble;
import cn.jwis.platform.plm.foundation.location.able.LocationAble;
import cn.jwis.platform.plm.foundation.model.entity.Property;
import cn.jwis.platform.plm.foundation.model.entity.VertexDef;
import cn.jwis.platform.plm.foundation.model.response.ModelPropertyWithRel;
import cn.jwis.platform.plm.foundation.model.service.ModelHelper;
import cn.jwis.platform.plm.foundation.numberrule.able.NumberAble;
import cn.jwis.platform.plm.foundation.numberrule.able.NumberAbleService;
import cn.jwis.platform.plm.foundation.numberrule.entity.NumberRule;
import cn.jwis.platform.plm.foundation.numberrule.generator.RuleExcutorParam;
import cn.jwis.platform.plm.foundation.numberrule.service.NumberRuleService;
import cn.jwis.platform.plm.foundation.pipeline.manager.AbleWorkCellPipelineManager;
import cn.jwis.platform.plm.foundation.pipeline.manager.command.BatchCreateCommand;
import cn.jwis.platform.plm.foundation.pipeline.manager.command.BatchFillPropertyCommand;
import cn.jwis.platform.plm.foundation.pipeline.manager.command.UpdateCommand;
import cn.jwis.platform.plm.foundation.pipeline.manager.constraints.ConstraintsFilter;
import cn.jwis.platform.plm.foundation.pipeline.manager.entity.ModelPlus;
import cn.jwis.platform.plm.foundation.relationship.PropertyAssign;
import cn.jwis.platform.plm.foundation.versionrule.able.LockAble;
import cn.jwis.platform.plm.foundation.versionrule.able.LockAbleService;
import cn.jwis.platform.plm.foundation.versionrule.able.VersionAble;
import cn.jwis.platform.plm.foundation.versionrule.able.VersionAbleService;
import cn.jwis.platform.plm.permission.helper.PermissionHelper;
import cn.jwis.platform.plm.permission.permission.enums.PermissionKeyEnum;
import cn.jwis.platform.plm.sysconfig.entity.ConfigItem;
import cn.jwis.platform.plm.sysconfig.preferences.PreferencesService;
import cn.jwis.product.pdm.cad.ecad.entity.ECADIteration;
import cn.jwis.product.pdm.cad.mcad.entity.MCADIteration;
import cn.jwis.product.pdm.customer.entity.DataDictionary;
import cn.jwis.product.pdm.customer.entity.assistant.SafeWrapAssist;
import cn.jwis.product.pdm.customer.repo.CustomerCommonRepo;
import cn.jwis.product.pdm.customer.service.interf.CommonService;
import cn.jwis.product.pdm.document.entity.DocumentIteration;
import cn.jwis.product.pdm.document.repo.DocumentIterationRepoImpl;
import cn.jwis.product.pdm.partbom.part.entity.PartIteration;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/8/30 15:37
 * @Email <EMAIL>
 */
@Slf4j
@Service
@Primary
@Transactional
public class CustomerCommonAbilityHelperImpl extends CommonAbilityHelperImpl implements NumberAbleService, SafeWrapAssist {

    private static final Logger logger = LoggerFactory.getLogger(CustomerCommonAbilityHelperImpl.class);

    @Autowired
    CommonAbilityService commonAbilityService;

    @Resource
    CustomerCommonRepo customerCommonRepo;

    @Autowired
    private CommonAbilityHelper commonAbilityHelper;

    @Autowired
    NumberRuleService numberRuleService;

    @Autowired
    CommonService commonService;

    @Autowired
    JWICommonService jwiCommonService;

    @Resource
    ClassificationPropertyHelper classificationPropertyHelper;

    @Autowired
    private InstanceHelper instanceHelper;

    private static final Set<String> ATTR_CHECK_SET = new HashSet<String>() {{
        add("cn_jwis_fzxs");
        add("cn_jwis_zldj");
        add("cn_jwis_sccj");
    }};

    @Autowired
    PreferencesService preferencesService;

    private static final Set<String> PRO_CODE_SET = new HashSet<String>() {{
        add("cn_jwis_bzh");
        add("cn_jwis_fzxs");
        add("cn_jwis_zldj");
        add("cn_jwis_sccj");
//        add("cn_jwis_smdj");
    }};

    private static final List<String> SKIP_CHECK_CODE_LIST = new ArrayList() {{
        //封装形式
        add("cn_jwis_fzxs");
        //质量等级
        add("cn_jwis_zldj");
        // 湿敏等级
        add("cn_jwis_smdj");
        //生产厂家
        add("cn_jwis_sccj");
        // 标准号
        add("cn_jwis_bzh");
    }};


    private List<String> needCheckPermissionType = Arrays.asList(
            MCADIteration.TYPE,
            ECADIteration.TYPE,
            PartIteration.TYPE,
            DocumentIteration.TYPE);

    @Override
    public List<LockAble> doCheckIn(Collection<LockAble> lockAbles) throws JWIException {
        for(LockAble lockAble : lockAbles){
            initNumberForUpdate(lockAble);
        }
        return super.doCheckIn(lockAbles);
    }

    @Override
    public LockAble doCheckIn(LockAble copyNode) throws JWIException {
        VersionAble latestVersion = (VersionAble) commonAbilityService.findLatestDetailEntity(copyNode.getOid(),
                copyNode.getType());
        initNumberForUpdate(copyNode);
        String lockSourceOid = copyNode.getLockSourceOid();
        String type = copyNode.getType();
        ModelAble sourceNode = this.findEntity(lockSourceOid, type);
        Assert.notNull(sourceNode, "Source node does not exists");
        LockAbleService lockAbleService = (LockAbleService) AbleWorkCellPipelineManager.getAbleService(type);
        VersionAbleService versionAbleService = (VersionAbleService)lockAbleService;
        VersionAble versionAble = versionAbleService.nextIteratedVersion((VersionAble)copyNode);
        if (ObjectUtils.isNotEmpty(latestVersion) && isLasted(latestVersion, versionAble)) {
            ((VersionAble) copyNode).setLatest(false);
        }
        this.commonAbilityService.dealReviewRelForCheckIn(Collections.singletonList(lockSourceOid), copyNode.getType());
        lockAbleService.checkIn(copyNode);
        return copyNode;
    }

    private boolean isLasted(VersionAble current, VersionAble next) {
        if (ObjectUtils.isEmpty(current) || ObjectUtils.isEmpty(next)) {
            return false;
        }
        if (StringUtils.equals(current.getVersion(), next.getVersion())) {
            return current.getIteratedVersion() > next.getIteratedVersion();
        } else {
            return StringUtils.compare(current.getVersion(), next.getVersion()) > 0;
        }
    }

    @Override
    public <T extends ModelAble> List<T> doCreate(List<T> entities) throws JWIException {
        // 检查权限
        checkCreatePermission(entities);
        for(T t : entities){
            if (t instanceof MCADIteration
                    || t instanceof DocumentIteration
                    || t instanceof PartIteration
                    || t instanceof ECADIteration) {
                getNoReapNumber(t);
            }
        }
        return doCreateOverride(entities,false);
    }


    public <T extends ModelAble> List<T> doCreateWithSkipCheck(List<T> entities,Boolean isCheck) throws JWIException {
        // 检查权限
        checkCreatePermission(entities);
        for(T t : entities){
            if (t instanceof MCADIteration
                    || t instanceof DocumentIteration
                    || t instanceof PartIteration
                    || t instanceof ECADIteration) {
                getNoReapNumber(t);
            }
        }
        return doCreateOverride(entities,isCheck);
    }

//    @Autowired
//    private CustomerCommonServiceImpl customerCommonService;
//
//    @Autowired
//    private FileService fileService;

    @Autowired
    ModelHelper modelHelper;

    public <T extends ModelAble> List<T> doCreateOverride(List<T> entities,boolean skipCheck) throws JWIException {
        Map<String, List<T>> detalModel2Entity = CollectionUtil.splitToMapList(entities, (item) -> {
            String modelDefinition = item.getModelDefinition();
            return StringUtil.isBlank(modelDefinition) ? item.getType() : modelDefinition;
        });
        String detailModel = null;
        String type = null;
        ModelPlus modelPlus = null;
        List<T> sameModelEntities = null;
        Iterator var7 = detalModel2Entity.entrySet().iterator();

        while(var7.hasNext()) {
            Map.Entry<String, List<T>> entry = (Map.Entry)var7.next();
            sameModelEntities = (List)entry.getValue();
            detailModel = entry.getKey();
            modelPlus = this.modelHelper.getModelDetail(detailModel);
            type = (sameModelEntities.get(0)).getType();
            attributeSynthesis((ModelAble)sameModelEntities.get(0), modelPlus);
            AbleWorkCellPipelineManager.excute(new BatchFillPropertyCommand(type, sameModelEntities, modelPlus));
            String checkResult = checkPropertiesConstraints(sameModelEntities, modelPlus,skipCheck);
            Assert.isBlank(checkResult, checkResult);
            AbleWorkCellPipelineManager.excute(new BatchCreateCommand(type, sameModelEntities, modelPlus));
        }

        return entities;
    }

    private <T extends ModelAble> String checkPropertiesConstraints(Collection<T> entities, ModelPlus modelPlus, boolean skipCheck) {
        long startTime = System.currentTimeMillis();
        if (modelPlus != null && !CollectionUtil.isEmpty(modelPlus.getPropertyWithRel())) {
            if (CollectionUtil.isEmpty(entities)) {
                return "";
            } else {
                StringBuilder stringBuilder = new StringBuilder();
                Map<String, PropertyDescriptor> propName2Pds = getClassPropertyDescriptorsMap(((ModelAble)CollectionUtil.getFirst(entities)).getClass());
                Iterator var7 = entities.iterator();

                while(var7.hasNext()) {
                    ModelAble modelAble = (ModelAble)var7.next();
                    stringBuilder.append(checkPropertiesConstraints(modelAble, modelPlus, propName2Pds, skipCheck));
                }

                if (stringBuilder.length() > 0) {
                    return stringBuilder.toString();
                } else {
                    logger.info("验证属性约束执行时间：{}ms", System.currentTimeMillis() - startTime);
                    return "";
                }
            }
        } else {
            return "";
        }
    }

    private Map<String, PropertyDescriptor> getClassPropertyDescriptorsMap(Class clazz) {
        BeanInfo beanInfo = null;

        try {
            beanInfo = Introspector.getBeanInfo(clazz, Object.class);
        } catch (IntrospectionException var9) {
            throw new JWIServiceException(var9);
        }

        PropertyDescriptor[] pds = beanInfo.getPropertyDescriptors();
        Map<String, PropertyDescriptor> propName2PDs = new HashMap();
        PropertyDescriptor[] var5 = pds;
        int var6 = pds.length;

        for(int var7 = 0; var7 < var6; ++var7) {
            PropertyDescriptor pd = var5[var7];
            propName2PDs.put(pd.getName(), pd);
        }

        return propName2PDs;
    }

    private String checkPropertiesConstraints(ModelAble entity, ModelPlus modelPlus, Map<String, PropertyDescriptor> propName2Pds,boolean skipCheck) {
        if (modelPlus != null && !CollectionUtil.isEmpty(modelPlus.getPropertyWithRel())) {
            StringBuilder stringBuilder = new StringBuilder();
            JSONObject jsonObject = findExtensionContent(entity);
            List<ModelPropertyWithRel> propertyWithRel = modelPlus.getPropertyWithRel();
            Iterator var7 = propertyWithRel.iterator();

            while(var7.hasNext()) {
                ModelPropertyWithRel mpr = (ModelPropertyWithRel)var7.next();
                String targetAttr = mpr.getCode();
                boolean isSystemProperty = mpr.isSystemDefault();
                Object value = null;
                if (isSystemProperty) {
                    value = matchAttrValue(targetAttr, entity, propName2Pds);
                } else {
                    value = jsonObject.get(targetAttr);
                }

                String checkResult = ConstraintsFilter.execute(targetAttr, mpr.getDisplayName(), value, mpr.getRelationship());
                if (StringUtil.isNotBlank(checkResult)) {
                    stringBuilder.append(checkResult);
                }
            }

            ClassificationAble classificationAble = findClsPropContent(entity);
            if (classificationAble != null && StringUtil.isNotBlank(classificationAble.getClsOid())) {
                List<ClsPropertyWithRel> clsProps = this.classificationPropertyHelper.findAllPropByClsOid(classificationAble.getClsOid());
                JSONObject clsJSON = Optional.ofNullable(classificationAble.getClsProperty()).orElse(new JSONObject());
                Iterator var18 = clsProps.iterator();
                while(var18.hasNext()) {
                    ClsPropertyWithRel clsProp = (ClsPropertyWithRel)var18.next();
                    if(skipCheck && SKIP_CHECK_CODE_LIST.contains(clsProp.getCode())) {
                        continue;
                    }
                    Object value = clsJSON.get(clsProp.getCode());
                    String checkResult = ConstraintsFilter.execute(clsProp.getCode(), clsProp.getDisplayName(), value, clsProp.getRelationship());
                    if (StringUtil.isNotBlank(checkResult)) {
                        stringBuilder.append(checkResult);
                    }
                }
            }

            if (stringBuilder.length() > 0) {
                String identified = getIdentified(entity);
                return "[" + identified + "]:" + stringBuilder;
            } else {
                return "";
            }
        } else {
            return "";
        }
    }

    private String getIdentified(ModelAble entity) {
        String modelType = StringUtil.isBlank(entity.getModelDefinition()) ? entity.getType() : entity.getModelDefinition();
        JSONObject data = (JSONObject)JSONObject.toJSON(entity);
        String identified = data.getString("name");
        return StringUtil.isNotBlank(identified) ? modelType + "-名称：" + identified : modelType;
    }

    private ClassificationAble findClsPropContent(ModelAble entity) {
        return entity instanceof ClassificationAble ? (ClassificationAble)entity : null;
    }

    private JSONObject findExtensionContent(ModelAble entity) {
        JSONObject jsonObject = null;
        if (entity instanceof ExtendedPropertyAble) {
            jsonObject = ((ExtendedPropertyAble)entity).getExtensionContent();
        }

        return jsonObject == null ? new JSONObject() : jsonObject;
    }

    private static Object matchAttrValue(String attrName, ModelAble entity, Map<String, PropertyDescriptor> propName2Pds) {
        Object value = null;

        try {
            if ("classificationInfo".equals(attrName)) {
                attrName = "clsOid";
            }

            if ("locationInfo".equals(attrName)) {
                attrName = "containerOid";
            }

            PropertyDescriptor property = (PropertyDescriptor)propName2Pds.get(attrName);
            if (property != null) {
                Method readMethod = property.getReadMethod();
                value = readMethod.invoke(entity);
            }
        } catch (Exception var6) {
            logger.error("checkSysPropertiesConstraints-get value from entity error:", var6);
        }

        return value;
    }

    private <T extends ModelAble> void attributeSynthesis(T entity, ModelPlus modelPlus) {
        List<AttributeRule> attrByModel = findParentConfig(modelPlus.getModel());
        if (CollectionUtil.isNotEmpty(attrByModel)) {
            Iterator var4 = attrByModel.iterator();

            while(var4.hasNext()) {
                AttributeRule attributeRule = (AttributeRule)var4.next();
                StringBuilder result = new StringBuilder();
                List<AttributeSegment> attributeSegments = attributeRule.getAttributeSegments();
                JSONObject extensionContent = (JSONObject)ReflectUtils.invokeGet(entity, "extensionContent");
                JSONObject clsProperty = (JSONObject)ReflectUtils.invokeGet(entity, "clsProperty");
                String attrType = checkFieldExist(modelPlus, attributeRule.getCompositeAttr());
                if (CollectionUtil.isNotEmpty(attributeSegments) && StringUtil.isNotEmpty(attrType)) {
                    attributeSegments.sort(Comparator.comparing(AttributeSegment::getOrder));
                    Iterator var11 = attributeSegments.iterator();

                    while(var11.hasNext()) {
                        AttributeSegment attributeSegment = (AttributeSegment)var11.next();
                        Object value;
                        if ("systemProp".equals(attributeSegment.getType())) {
                            if ("classificationInfo".equals(attributeSegment.getRule().getString("value"))) {
                                value = ReflectUtils.invokeGet(entity, "clsDisplayName");
                            } else {
                                value = ReflectUtils.invokeGet(entity, attributeSegment.getRule().getString("value"));
                            }

                            result.append(value != null ? value : "");
                        } else if ("connector".equals(attributeSegment.getType())) {
                            result.append(attributeSegment.getRule().getString("value"));
                        } else if ("extendProp".equals(attributeSegment.getType())) {
                            if (extensionContent != null) {
                                value = extensionContent.get(attributeSegment.getRule().getString("value"));
                                result.append(value != null ? value : "");
                            }
                        } else if ("classificationProp".equals(attributeSegment.getType()) && clsProperty != null) {
                            value = clsProperty.get(attributeSegment.getRule().getString("value"));
                            result.append(value != null ? value : "");
                        }
                    }
                }

                if (StringUtil.isNotEmpty(result)) {
                    if ("system".equals(attrType)) {
                        ReflectUtils.invokeSet(entity, attributeRule.getCompositeAttr(), result.toString());
                    } else if ("extend".equals(attrType) && extensionContent != null) {
                        extensionContent.put(attributeRule.getCompositeAttr(), result.toString());
                        ReflectUtils.invokeSet(entity, "extensionContent", extensionContent);
                    }
                }
            }
        }

    }

    private String checkFieldExist(ModelPlus allModel, String compositeAttr) {
        String attrType = null;
        if (CollectionUtil.isNotEmpty(allModel.getPropertyWithRel())) {
            List<ModelPropertyWithRel> properties = allModel.getPropertyWithRel();
            List<Property> collect = (List)properties.stream().filter((item) -> {
                return item.getCode().equals(compositeAttr);
            }).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(collect)) {
                if (((Property)collect.get(0)).isSystemDefault()) {
                    attrType = "system";
                } else {
                    attrType = "extend";
                }
            }
        }

        return attrType;
    }

    @Autowired
    private AttributeRuleHelper attributeRuleHelper;

    private List<AttributeRule> findParentConfig(VertexDef model) {
        List<AttributeRule> attrByModel = attributeRuleHelper.findAttrByModel(model.getCode());
        if (CollectionUtil.isEmpty(attrByModel)) {
            VertexDef parent = (VertexDef)CollectionUtil.getFirst(this.commonAbilityService.findIterByTo(model.getOid(), "VertexDef", "SUB", "VertexDef"));
            if (parent != null) {
                return this.findParentConfig(parent);
            }
        }

        return attrByModel;
    }

    @Autowired
    DocumentIterationRepoImpl documentIterationRepo;

    @Override
    public <T extends ModelAble> T doCreate(T entity) throws JWIException {
        return this.doCreate(entity,false);
    }

    @Override
    public <T extends ModelAble> T doCreate(T entity,boolean skipCheck) throws JWIException {
        checkCreatePermission(Arrays.asList(entity));

        if(entity instanceof DocumentIteration){
            DocumentIteration dto = (DocumentIteration) entity;
            if(dto.getPrimaryFile() != null){
                StringBuilder stringBuilder = new StringBuilder();
                dto.getPrimaryFile().stream().forEach(it -> {
                    if(it.getName() != null && it.getOid() == null)
                        stringBuilder.append("主文件上传失败 请重新上传主文件");
                });
                if(stringBuilder.length() != 0)
                    throw new JWIException(stringBuilder.toString());
            }

            String number = ((DocumentIteration) entity).getNumber();
            if(number != null){
                DocumentIteration exsitDoc = documentIterationRepo.findByNumber(number);
                if(exsitDoc != null)
                    throw new JWIException("已存在相同编码的文档");

            }

        }

        if (entity instanceof MCADIteration
                || entity instanceof DocumentIteration
                || entity instanceof PartIteration
                || entity instanceof ECADIteration) {
            getNoReapNumber(entity);

        }

        T createData = super.doCreate(entity,skipCheck);
        return createData;

    }

    @Override
    public <T extends ModelAble> T doUpdate(T entity) throws JWIException {
        // 分类属性为手填场景更新时根据已配置数据字典进行替换 并重新生成编码
        rebuildNumberWithPro(entity);
        ModelPlus modelPlus = this.modelHelper.getModelDetail(entity);
        this.attributeSynthesis(entity, modelPlus);
        if (!(entity instanceof PartIteration)
                || !StrUtil.contains(((PartIteration) entity).getNumber(), "LS")) {
            log.info("当前entity:{}", JSONUtil.toJsonStr(entity));
            String checkResult = this.checkPropertiesConstraints(Collections.singleton(entity), modelPlus);
            Assert.isBlank(checkResult, checkResult);
        }
        AbleWorkCellPipelineManager.excute(new UpdateCommand(entity, modelPlus));
        return entity;
//        return super.doUpdate(entity);
    }

    private <T extends ModelAble> String checkPropertiesConstraints(Collection<T> entities, ModelPlus modelPlus) {
        return this.checkPropertiesConstraints(entities, modelPlus, false);
    }

    // 更新时根据数据字段重新生成编码   依赖分类数据字段全部就绪才会更新
    private <T extends ModelAble> void rebuildNumberWithPro(T entity) {
        if(!(entity instanceof NumberAble)){
            return;
        }
        if(entity instanceof PartIteration){
            String clsCode = ((PartIteration)entity).getClsCode();
            // 临时分类不进行处理
            if(StringUtil.isBlank(clsCode) || "CADCHECKIN".equals(clsCode)){
                return;
            }
            PartIteration part = (PartIteration) entity;
            String number = part.getNumber();
            JSONObject clsProperty = part.getClsProperty();
            String clsDisplayName = part.getClsDisplayName();
            if(("RM0499其他标准件").equals(clsDisplayName) || "RM0408螺套".equals(clsDisplayName)) {
                return;
            }
            Matcher name = pattern.matcher(clsDisplayName);
            if (name.find()) {  // 确保匹配成功
                String prefix = name.group(1);
                if ("RM0325".equals(prefix)) {
                    return;
                }
            }
            // key为分类属性字段  value为数据字典内 属性清单ParentCode
            Map<String,String> codeMap = new HashMap<>();

            List<ClsPropertyWithRel> proList = classificationPropertyHelper.findAllPropByClsOid(part.getClsOid());
            for (ClsPropertyWithRel clsPropertyWithRel : proList) {
                PropertyAssign propertyAssign = clsPropertyWithRel.getRelationship();
                if(DataDictionary.TYPE.toLowerCase(Locale.ROOT).equals(propertyAssign.getConstraintType())) {
                    JSONObject context = propertyAssign.getConstraintContext();
                    String code = context.getString(DataDictionary.TYPE.toLowerCase());
                    codeMap.put(clsPropertyWithRel.getCode(),code);
                }
            }

            Map<String, List<DataDictionary>> parentCodeDataDictionaryListMap =
                    jwiCommonService.dynamicQuery(DataDictionary.TYPE,
                            Condition.where(
                                    "parentCode").in(codeMap.values()).and(Condition.where("enable").eq(true)),
                            DataDictionary.class).stream().collect(Collectors.groupingBy(DataDictionary::getParentCode));
            // 检查是否需要重新生成编码
            boolean toInitNumber = inRage(clsProperty, codeMap, parentCodeDataDictionaryListMap,number);
            boolean draftRuleFlag = isDraftRuleFlag(part);
            if (toInitNumber || draftRuleFlag) {
                ((PartIteration) entity).setNumber(null);
                getNoReapNumber(entity);
            }
        }
    }

    /**
     * 判断草稿状态下的物料是否可以重新编码
     * @param part
     * @return
     */
    private boolean isDraftRuleFlag(PartIteration part) {
        boolean draftRuleFlag = Boolean.FALSE;

        // 新增规则：如果生命周期状态为 "Draft"，允许重新生成编码
        String lifecycleStatus = part.getLifecycleStatus();
        if ("Draft".equals(lifecycleStatus)) {
            // 查询上一版本的物料信息
            List<InstanceEntity> history = instanceHelper.findHistory(part.getOid(), PartIteration.TYPE);
            log.info("当前历史版本信息：{}", JSONUtil.toJsonStr(history));
            // 过滤掉与当前版本 displayVersion 相同的历史记录
            history = history.stream()
                    .filter(entity1 -> !entity1.getDisplayVersion().equals(part.getDisplayVersion()))
                    .collect(Collectors.toList());

            // 获取过滤后的第一个历史版本
            if (!history.isEmpty()) {
                InstanceEntity firstHistory = history.get(0);
                log.info("获取到的历史版本信息：{}", JSONUtil.toJsonStr(firstHistory));

                // 查询详细信息
                PartIteration historyPart = (PartIteration) commonAbilityService.findDetailEntity(firstHistory.getOid(), firstHistory.getType());

                // 1. 对比 clsOid 属性
                if (!part.getClsOid().equals(historyPart.getClsOid())) {
                    draftRuleFlag = true; // 类别不同，需要重新编码
                } else if (part.getNumber().startsWith("RM") || part.getNumber().contains("LS")) {
                    // 2. 比较 extensionContent 属性
                    JSONObject currentExt = part.getClsProperty();
                    JSONObject historyExt = historyPart.getClsProperty();

                    // 定义需要比较的属性列表

                    for (String key : PRO_CODE_SET) {
                        // 比较属性值是否相同
                        if (!Objects.equals(currentExt.getString(key), historyExt.getString(key))) {
                            draftRuleFlag = true; // 属性值发生变化，需要重新编码
                            log.info("属性 {} 发生变化，需要重新编码", key);
                            break;
                        }
                    }
                }
            }

        }
        return draftRuleFlag;
    }

    private boolean inRage(JSONObject clsProperty, Map<String, String> codeMap, Map<String,
            List<DataDictionary>> parentCodeDataDictionaryListMap, String number) {
        boolean needReplace = false;
        // 更新分类属性用临时json
        JSONObject updateClsProperty = new JSONObject();
        for (String proCode : PRO_CODE_SET) {
            // 当前分类属性
            String name = clsProperty.getString(proCode);
            if (StringUtils.isNotBlank(name)) {
                // 数据字典父级code
                String dictCode = codeMap.get(proCode);
                List<DataDictionary> dataDictionaryList = parentCodeDataDictionaryListMap.get(dictCode);
                if (CollectionUtils.isEmpty(dataDictionaryList)) {
                    return false;
                }
                Map<String, String> nameCodeMap =
                        dataDictionaryList.stream().collect(Collectors.toMap(DataDictionary::getDisplayName,
                                DataDictionary::getCode, (existingValue, newValue) -> existingValue));
                // 当前存储值为text 需要进行转换
                if (nameCodeMap.containsKey(name)) {
                    updateClsProperty.put(proCode, nameCodeMap.get(name));
                    //如果是临时编码（包括 1、RM0301-LS00001物料编码申请流程 2、LS00001 这两种场景）才重新生成编码
                    //解决历史物料中 clsProperty中保存的值是标签值，非code导致的重新生成物料编码的问题
                    if (number.contains("LS")) {
                        needReplace = true;
                    }
                    // 存储值不在数据字典 text,code范围内 跳过处理逻辑
                } else if (!nameCodeMap.values().contains(name)) {
                    return false;
                }
            }
        }
        if(needReplace) {
            clsProperty.putAll(updateClsProperty);
        }
        return needReplace;
    }

    @Override
    public <T extends ModelAble> Collection<T> doUpdate(List<T> entities) throws JWIException {
        return super.doUpdate(entities);
    }

    public <T extends ModelAble> void initNumberForUpdate(T entity){
        // 如果是临时分类部件，则出临时编码
        if(entity instanceof PartIteration){
            String clsCode = ((PartIteration)entity).getClsCode();
            String lockSourceOid = ((PartIteration)entity).getLockSourceOid();
            if(StringUtil.isNotBlank(lockSourceOid)) {
                PartIteration preIteration = (PartIteration) commonAbilityService.findDetailEntity(lockSourceOid, PartIteration.TYPE);
                if (StringUtil.isNotBlank(clsCode)
                        && "CADCHECKIN".equals(preIteration.getClsCode()) && !"CADCHECKIN".equals(clsCode)) {
                    PartIteration dbPart = (PartIteration) commonAbilityService.findDetailEntity(entity.getOid(),entity.getType());
                    log.info("临时编码重新生成 oid:{} number:{}",dbPart.getOid(),dbPart.getNumber());
                    dbPart.setNumber(null);
                    String modelDefinition = queryModelDefinitionByClsOid(dbPart.getClsOid());
                    if(StringUtils.isNotBlank(modelDefinition)) {
                        log.info("临时编码重新生成 oid:{} 设置modelDefinition为:",dbPart.getOid(),modelDefinition);
                        dbPart.setModelDefinition(modelDefinition);
                    } else {
                        log.info("临时编码重新生成 oid:{} 未查询到新分类对应子模型类型:",dbPart.getOid());
                    }
                    getNoReapNumber(dbPart);
                    log.info("临时编码重新生成 oid:{} 新生成编码:{}", dbPart.getOid(), dbPart.getNumber());
                    super.doUpdate(dbPart);
                }
            }
        }
    }

    private String queryModelDefinitionByClsOid(String oid) {
        Map<String,Classification> map =
                customerCommonRepo.queryTreeByChildOid(oid).stream().collect(Collectors.toMap(Classification::getParentOid,
                        v->v));
        Classification root = map.get(null);
        if (ObjectUtils.isEmpty(root)) {
            return StringUtils.EMPTY;
        }
        Classification parent = map.get(root.getOid());
        if (ObjectUtils.isEmpty(parent)) {
            return StringUtils.EMPTY;
        }
        Condition condition = Condition.where("displayName").eq(parent.getDisplayName());
        VertexDef vertexDef = jwiCommonService.dynamicQueryOne(VertexDef.TYPE,condition,VertexDef.class);
        if (ObjectUtils.isEmpty(vertexDef)) {
            return StringUtils.EMPTY;
        }
        return vertexDef.getCode();
    }

    private static final String BZJ = "RM0499", LT = "RM0408", RM = "RM";
    private static final String CN_JWIS_LS = "cn_jwis_ls";
    private static final String CN_JWIS_YCL = "cn_jwis_ycl";
    private static Pattern pattern = Pattern.compile("(RM\\d*).*");

    private static final List<String> CODE_AND_SEQ_PRE_LIST = new ArrayList<String>(){{
        add("RM0325");
        add("RM0411");
        add("RM0412");
        add("RM0413");
        add("RM0414");
        add("RM0415");
        add("RM0416");
    }};

    private boolean inConfigCode(String prefix,String code,List<String> defaultList) {
        List<String> handlerList = defaultList == null ? Lists.newArrayList() : defaultList;
        ConfigItem configItem = preferencesService.queryConfigValue(code);
        if (configItem != null) {
            if (StringUtils.isNotBlank(configItem.getValue())) {
                String[] value = configItem.getValue().split("\\|");
                if (value.length > 0) {
                    handlerList = Arrays.asList(value);
                }
            }
        }
        String res =
                handlerList.stream().filter(item->StringUtils.equalsIgnoreCase(prefix,item)).findAny().orElse(null);
        return StringUtils.isNotBlank(res);
    }

    private boolean isCodeAndPro(String prefix) {
        return inConfigCode(prefix,"code_with_pro_pre_list",null);
    }

    private boolean isCodeAndSeq(String prefix) {
        return inConfigCode(prefix,"code_and_seq_pre_list",CODE_AND_SEQ_PRE_LIST);
    }

    private <T extends ModelAble> void getNoReapNumber(T entity){
        initNumber(entity);
        if(entity instanceof NumberAble) {
            NumberAble numberAble = (NumberAble)entity;
            String number = numberAble.getNumber();
            logger.info("当前entity编码==>" + number + " 当前实体信息==>" + JSONUtil.toJsonStr(entity));
            List<ModelAble> exists = this.jwiCommonService.dynamicQuery(entity.getType(), Condition.where("number").eq(number));
            if(CollectionUtil.isNotEmpty(exists)){
                logger.info("当前entity编码重复 重新获取编码 当前entity==>" + JSONUtil.toJsonStr(entity));
                numberAble.setNumber(null);
                getNoReapNumber(entity);
            }
        }
    }

    // 银河需求在编码前加上产品代号
    public <T extends ModelAble> void initNumber(T entity){
        String cpdh = "";
        // 非带编码实体则不处理
        if(!(entity instanceof NumberAble)){
            return;
        }
        if(StringUtil.isNotBlank(((NumberAble)entity).getNumber())){
            return;
        }
        // 如果是临时分类部件，则出临时编码
        if(entity instanceof PartIteration){
            String clsCode = ((PartIteration)entity).getClsCode();
            if(StringUtil.isBlank(clsCode) || "CADCHECKIN".equals(clsCode)){
                NumberRule numberRule = numberRuleService.findByCode("cn_jwis_ls");
                commonAbilityService.nextNumber(((NumberAble)entity),numberRule);
                return;
            }
            PartIteration part = (PartIteration) entity;
            JSONObject clsProperty = part.getClsProperty();
            String clsDisplayName = part.getClsDisplayName();
            String prefix = customerCommonRepo.queryClsRelCodeByOid(part.getClsOid()) ;
            //如果生产厂家、质量等级、封装形式中有一项是 手动输入，那么设置临时编码
            boolean toGenTempNumber = checkClsProperty(part, ATTR_CHECK_SET);
            if (toGenTempNumber && (prefix == null || !prefix.contains("PT"))) {
                //增加 业务前缀，方便后续发起流程的校验
                 prefix = commonAbilityService.nextNumber(part).split("-")[0];
                part.setNumber(prefix + "-" + nextNumber(CN_JWIS_LS));
            }else {
            if(("RM0499其他标准件").equals(clsDisplayName)) {
                part.setNumber(BZJ + "-" + nextNumber(BZJ));
            } else if(("RM0408螺套").equals(clsDisplayName)) {
                part.setNumber(LT + "-" + nextNumber(LT));
            } else if(isCodeAndSeq(prefix)) {
                log.info("init number isCodeAndSeq rule is cn_jwis_ycl");
                NumberRule numberRule = numberRuleService.findByCode(CN_JWIS_YCL);
                String[] numberArr = commonAbilityService.nextNumber(part,numberRule).split("-");
                part.setNumber(numberArr[0] + "-" + numberArr[1]);
            } else if(isCodeAndPro(prefix)) {
                log.info("init number isCodeAndProrule is cn_jwis_ycl");
                NumberRule numberRule = numberRuleService.findByCode(CN_JWIS_YCL);
                commonAbilityService.nextNumber(part,numberRule);
            } else if(clsDisplayName != null && clsDisplayName.startsWith(RM)){
                log.info("init number startWith RM rule is default");
                commonAbilityService.nextNumber(part);
            } else {
                log.info("init number rule is default");
                commonAbilityService.nextNumber(((NumberAble)entity));
            }
            }
            //1、属于Part 2、重新生成编码 3、需要同事重新生成转换后编码 CN_YH_U9CODE
            String number = part.getNumber();
            String CN_YH_U9CODE = processCode(number);

            JSONObject extensionContent = part.getExtensionContent();
            extensionContent.put("CN_YH_U9CODE", CN_YH_U9CODE);
        } else {
            commonAbilityService.nextNumber(((NumberAble)entity));
        }


        // 去除编码末尾的---
        ((NumberAble)entity).setNumber(trimNumber(((NumberAble)entity).getNumber()));
        if(!(entity instanceof MCADIteration || entity instanceof DocumentIteration)) {
            return;
        }

        Container container = (Container) commonAbilityService.findDetailEntity(
                ((LocationAble)entity).getContainerOid(),
                ((LocationAble)entity).getContainerType());
        JSONObject ex = container.getExtensionContent();
        if (ex == null) {
            return;
        }
        cpdh = ex.getString("cn_jwis_cpdh");
        if (StringUtil.isBlank(cpdh)) {
            return;
        }

        if(entity instanceof MCADIteration) {
            MCADIteration mi = (MCADIteration)entity;
            mi.setNumber(cpdh + mi.getNumber());
        }else if(entity instanceof DocumentIteration){
            // 根据文件夹获取分系统代号和单机号等属性段
            String attrNumberUnit = getAttrNumberUnit((DocumentIteration)entity);
            DocumentIteration di = (DocumentIteration)entity;
            if ("JWIGeneralDocument".equals(di.getModelDefinition())) {
                di.setNumber(cpdh + attrNumberUnit + di.getNumber());
            } else if ("JWIProductionDocument".equals(di.getModelDefinition())) {
                String number = di.getNumber();
                if (!number.contains("-")) {
                    return;
                }
                String unit[] = number.split("-");
                di.setNumber(unit[0] + attrNumberUnit + cpdh + "-" + unit[1]);
            }
            DocumentIteration dto = (DocumentIteration) entity;
            if(dto.getExtensionContent() != null && "外协类文件".equals(dto.getExtensionContent().getString("source"))){
                if(!StringUtils.isEmpty(dto.getNumber()))
                    dto.setNumber("wx-" + dto.getNumber());
            }
        }
    }

    public String nextNumber(String ruleCode) {
        NumberRule numberRule = numberRuleService.findByCode(ruleCode);
        if (null == numberRule) {
            throw new JWIException("number rule not exist");
        }
        RuleExcutorParam param = new RuleExcutorParam();
        param.setRuleCode(ruleCode);
        param.setType(ruleCode);
        param.setModelDefinition(ruleCode);
        param.setReFindModelData(false);
        return nextNumber(numberRule, param);
    }

    private String getAttrNumberUnit(DocumentIteration doc) throws JWIException {
        String attrsUnit = "";
        try {
            List<Folder> folders = commonService.getFolderPath(doc.getCatalogType(), doc.getCatalogOid());
            if(CollectionUtil.isEmpty(folders)){
                return "";
            }
            ListIterator<Folder> listIterator = folders.listIterator();
            while(listIterator.hasNext()){
                Folder folder = listIterator.next();
                JSONObject extensionContent = folder.getExtensionContent();
                // 产品文档是分系统代号参与编码；普通文档是分系+单机号参与编码
                if(extensionContent == null
                        || !extensionContent.containsKey("code")) {
                    continue;
                }
                if("JWIGeneralDocument".equals(doc.getModelDefinition())) {
                    attrsUnit = extensionContent.getString("code") + attrsUnit;
                }else if("JWIProductionDocument".equals(doc.getModelDefinition())) {
                    attrsUnit = extensionContent.getString("code");
                }
            }
        }catch (Exception e){
            throw new JWIException(e);
        }
        return attrsUnit;
    }

    private String trimNumber(String number) {
        if(number.endsWith("-")){
            number = number.substring(0,number.length()-1);
            return trimNumber(number);
        }else{
            return number;
        }
    }

    private <T extends ModelAble> void checkCreatePermission(List<T> entities){
        if (CollectionUtil.isEmpty(entities)) {
            return ;
        }
        List<LocationAble> locationAbles = new ArrayList<>();
        entities.stream().forEach(e->{
            if(e instanceof LocationAble) {
                locationAbles.add((LocationAble)e);
            }
        });
        if (CollectionUtil.isEmpty(locationAbles)) {
            return ;
        }
        UserDTO user = SessionHelper.getCurrentUser();
        Map<String,List<LocationAble>> entityMap = CollectionUtil.splitToMapList(locationAbles,LocationAble::getType);
        for(Map.Entry<String,List<LocationAble>> entry : entityMap.entrySet()) {
            String type = entry.getKey();
            if(!needCheckPermissionType.contains(type)){
                continue;
            }
            ModelAble first = entry.getValue().get(0);
            if (first instanceof LocationAble) {
                LocationAble locationAble = (LocationAble) first;
                JSONObject param = new JSONObject();
                param.put("tenantOid", user.getTenantOid());
                param.put("containerOid", locationAble.getContainerOid());
                param.put("catalogOid", locationAble.getCatalogOid());
                param.put("catalogType", locationAble.getCatalogType());
                param.put("masterType", type);
                Set<String> modelTypes = CollectionUtil.mapToSet(entry.getValue(), ModelAble::getModelDefinition);
                for (String modelType : modelTypes) {
                    param.put("modelDefinition", modelType);
                    boolean hasAccess = PermissionHelper.hasCreatePermission(param, PermissionKeyEnum.CREATE, user);
                    Container container = (Container) commonAbilityHelper.findDetailEntity(locationAble.getContainerOid(), Container.TYPE);
                    // 产品库或者电子元器件是公开库，不考虑权限，文件夹下都可以创建对象
                    if (!container.isPrivateFlag()) {
                        hasAccess = true;
                    }
                    Assert.isTrue(hasAccess, "User " + user.getAccount() + " has no " + PermissionKeyEnum.CREATE + " permission for " + modelType);
                }
            }
        }

    }

    private <T extends ModelAble> void checkUpdatePermission(List<T> entities) {
        if (CollectionUtil.isEmpty(entities)) {
            return ;
        }
        UserDTO user = SessionHelper.getCurrentUser();
        List<BaseEntity> baseEntities = new ArrayList<>();
        entities.stream().forEach(e->{
            if(e instanceof NumberAble && needCheckPermissionType.contains(e.getType())) {
                baseEntities.add((BaseEntity)e);
            }
        });
        for (BaseEntity baseEntity : baseEntities) {
            boolean hasAccess = PermissionHelper.validate(baseEntity, PermissionKeyEnum.EDIT, user);
            Assert.isTrue(hasAccess, "User " + user.getAccount() + " has no " + PermissionKeyEnum.EDIT + " permission for " + baseEntity.getOid());

        }
    }


    /**
     * 校验 生产厂家、质量等级、封装形式是否为手动输入
     *
     * @param part
     * @param attrCheckSet
     * @return
     */
    private boolean checkClsProperty(PartIteration part, Set<String> attrCheckSet) {
        boolean needReGenerate = false;
        JSONObject clsProperty = part.getClsProperty();
        JSONObject updateClsProperty = new JSONObject();
        Map<String,String> codeMap = new HashMap<>();
        List<ClsPropertyWithRel> proList = classificationPropertyHelper.findAllPropByClsOid(part.getClsOid());
        for (ClsPropertyWithRel clsPropertyWithRel : proList) {
            PropertyAssign propertyAssign = clsPropertyWithRel.getRelationship();
            if(DataDictionary.TYPE.toLowerCase(Locale.ROOT).equals(propertyAssign.getConstraintType())) {
                JSONObject context = propertyAssign.getConstraintContext();
                String code = context.getString(DataDictionary.TYPE.toLowerCase());
                codeMap.put(clsPropertyWithRel.getCode(),code);
            }
        }
        Map<String, List<DataDictionary>> parentCodeDataDictionaryListMap =
                jwiCommonService.dynamicQuery(DataDictionary.TYPE,
                        Condition.where("parentCode").in(codeMap.values()).and(Condition.where("enable").eq(true)),
                        DataDictionary.class).stream().collect(Collectors.groupingBy(DataDictionary::getParentCode));

        //增加对湿敏等级的检测替换
        attrCheckSet.add("cn_jwis_smdj");
        //增加对标准号的检测替换
        attrCheckSet.add("cn_jwis_bzh");
        // 更新分类属性用临时json
        for (String proCode : attrCheckSet) {
            // 当前分类属性
            String value = clsProperty.getString(proCode);
            if (StringUtils.isNotBlank(value)) {
                value = value.trim();
                // 数据字典父级code
                String dictCode = codeMap.get(proCode);
                List<DataDictionary> dataDictionaryList = parentCodeDataDictionaryListMap.get(dictCode);
                if (CollectionUtils.isEmpty(dataDictionaryList)) {
                    return false;
                }

                Map<String, String> nameCodeMap = new ConcurrentHashMap<>(
                        dataDictionaryList.stream().collect(Collectors.toMap(DataDictionary::getDisplayName,
                                DataDictionary::getCode, (existingValue, newValue) -> existingValue)));
                // 当前存储值为text 需要进行转换
                if (nameCodeMap.containsKey(value)) {
                    updateClsProperty.put(proCode, nameCodeMap.get(value));
                    clsProperty.putAll(updateClsProperty);
                } else if (!nameCodeMap.containsValue(value) && !proCode.equals("cn_jwis_smdj")) {
                    logger.info("当前物料名称：{}, 要check的属性:{} 新建字典值为：{}", part.getName(), proCode, value);
                    logger.info("当前字典编码map:{}", JSONUtil.toJsonStr(nameCodeMap));
                    return true;
                }
            }
        }

        return needReGenerate;
    }

    /**
     * 处理 Part 编号，去除 "-" 并去掉前缀 "R"
     *
     * @param code 原始编号
     * @return 处理后的编号
     */
    public  String processCode(String code) {
        if (code == null) {
            return "";
        }

        // 只有当 code 长度大于等于 18 时才进行处理
        if (code.length() >= 18) {
            // 去除所有的 "-" 符号
            code = code.replace("-", "");

            // 去掉开头的 'R'
            if (code.startsWith("R")) {
                code = code.substring(1);
            }
        }

        return code;
    }
}
