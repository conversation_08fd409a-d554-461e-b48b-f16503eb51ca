package cn.jwis.product.pdm.customer.service.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
     * 电缆版BOM条目数据结构
     */
@Data
@EqualsAndHashCode
public  class CableBOMEntry {
        private String level0 = ""; // BOM层级 0
        private String level1 = ""; // BOM层级 1
        private String level2 = ""; // BOM层级 2
        private String level3 = ""; // BOM层级 3
        private String level4 = ""; // BOM层级 4
        private String level5 = ""; // BOM层级 5
        private String level6 = ""; // BOM层级 6
        private String level7 = ""; // BOM层级 7
        private String subItemNumber = ""; // 子件项次
        private String materialNumber = ""; // 料号（必填项）
        private String convertedU9Code = ""; // 转化后U9编码
        private String tianTongJingDianCode = ""; // 天通精电编码
        private String productName = ""; // 品名
        private String specification = ""; // 规格
        private String packageForm = ""; // 封装形式
        private String manufacturer = ""; // 生产厂家
        private String qualityGrade = ""; // 质量等级
        private String materialForm = ""; // 料品形态
        private String quantity = ""; // 用量（必填项）
        private String position = ""; // 位号
        private String position1 = ""; // 位号1
        private String position2 = ""; // 位号2
        private String position3 = ""; // 位号3
        private String position4 = ""; // 位号4
        private String position5 = ""; // 位号5
        private String position6 = ""; // 位号6
        private String position7 = ""; // 位号7
        private String position8 = ""; // 位号8
        private String position9 = ""; // 位号9
        private String unit = ""; // 单位
        private String description = ""; // 物料描述

        /**
         * 设置对应层级的标记
         */
        public void setLevelMark(int level) {
            switch (level) {
                case 0: this.level0 = "*"; break;
                case 1: this.level1 = "*"; break;
                case 2: this.level2 = "*"; break;
                case 3: this.level3 = "*"; break;
                case 4: this.level4 = "*"; break;
                case 5: this.level5 = "*"; break;
                case 6: this.level6 = "*"; break;
                case 7: this.level7 = "*"; break;
                default: break; // 超过7层不处理
            }
        }

        /**
         * 设置位号分割，如果位号过长则分割到多个字段
         */
        public void setPositionSplit(java.util.List<String> positionParts) {
            if (positionParts == null || positionParts.isEmpty()) {
                return;
            }

            // 设置主位号
            this.position = positionParts.get(0);

            // 设置分割位号
            if (positionParts.size() > 1) this.position1 = positionParts.get(1);
            if (positionParts.size() > 2) this.position2 = positionParts.get(2);
            if (positionParts.size() > 3) this.position3 = positionParts.get(3);
            if (positionParts.size() > 4) this.position4 = positionParts.get(4);
            if (positionParts.size() > 5) this.position5 = positionParts.get(5);
            if (positionParts.size() > 6) this.position6 = positionParts.get(6);
            if (positionParts.size() > 7) this.position7 = positionParts.get(7);
            if (positionParts.size() > 8) this.position8 = positionParts.get(8);
            if (positionParts.size() > 9) this.position9 = positionParts.get(9);
        }
    }