package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.platform.plm.account.entity.tenant.Tenant;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.foundation.classification.entity.Classification;
import cn.jwis.platform.plm.foundation.classification.related.ClsLayout;
import cn.jwis.platform.plm.foundation.classification.responce.ClassificationTreeNode;
import cn.jwis.platform.plm.foundation.classification.responce.ClsPropertyWithRel;
import cn.jwis.platform.plm.foundation.classification.service.ClassificationHelper;
import cn.jwis.platform.plm.foundation.classification.service.ClassificationPropertyHelper;
import cn.jwis.platform.plm.foundation.model.entity.VertexDef;
import cn.jwis.platform.plm.foundation.model.response.ModelTreeNode;
import cn.jwis.platform.plm.foundation.model.service.ModelService;
import cn.jwis.platform.plm.foundation.relationship.PropertyAssign;
import cn.jwis.platform.plm.foundation.relationship.Sub;
import cn.jwis.product.pdm.customer.entity.CreoClassNode;
import cn.jwis.product.pdm.customer.entity.CreoPropertyNode;
import cn.jwis.product.pdm.customer.entity.DataDictionary;
import cn.jwis.product.pdm.customer.repo.CustomerCommonRepo;
import cn.jwis.product.pdm.partbom.part.entity.Part;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> yefei
 * @Date ： 2024/3/26
 * @Description :
 */

@Slf4j
@Component
public class CustomerClassificationService {
    @Resource
    ModelService modelService;
    @Resource
    JWICommonService jwiCommonService;

    @Resource
    CustomerCommonRepo customerCommonRepo;

    @Resource
    ClassificationHelper classificationHelper;

    @Resource
    ClassificationPropertyHelper classificationPropertyHelper;

    private static final String KEY_TXT = "txt";

    private static final String KEY_VALUE = "value";

    private static final String KEY_CODE = "code";

    private static final String KEY_DISPLAY_NAME = "displayName";

    public List<CreoClassNode> queryClassification() {
        List<ClassificationTreeNode> rootList = getClassificationTreeNodes();
        Set<String> oidSet = new HashSet<>();
        getClsOidSet(rootList,oidSet);
        Map<String, List<ClsPropertyWithRel>> prMap =
                classificationPropertyHelper.fuzzyByClsOidsRel(oidSet.stream().collect(Collectors.toList()));
        return buildRootInfoList(rootList, prMap);
    }

    private List<ClassificationTreeNode> getClassificationTreeNodes() {
        Condition condition = Condition.where(KEY_CODE).eq(Part.TYPE);

        List<ModelTreeNode> result = this.modelService.searchTree(StringUtils.EMPTY, Sub.TYPE, condition);
        Set<String> nameSet =
                result.stream().findAny().get().getChildren().stream().map(VertexDef::getDisplayName).collect(Collectors.toSet());

        Classification partClassification = jwiCommonService.dynamicQueryOne(Classification.TYPE,Condition.where(
                KEY_DISPLAY_NAME).eq("部件"),Classification.class);

        Tenant tenant = jwiCommonService.dynamicQueryOne(Tenant.TYPE, null, Tenant.class);
        UserDTO userDTO = new UserDTO();
        userDTO.setTenantOid(tenant.getOid());
        SessionHelper.addCurrentUser(userDTO);
        List<ClassificationTreeNode> rootList = classificationHelper.searchTree(partClassification.getOid(),
                StringUtils.EMPTY);
        // 过滤模型上包含定义的分类清单
        rootList = rootList.stream().findAny().get().getChildren().stream().filter(item->nameSet.contains(item.getDisplayName())).collect(Collectors.toList());
        return rootList;
    }

    private List<CreoClassNode> buildRootInfoList(List<ClassificationTreeNode> baseNodeList, Map<String, List<ClsPropertyWithRel>> prMap) {
        List<CreoClassNode> result = new ArrayList<>();
        Map<String, List<CreoPropertyNode>> waitQueryList = new HashMap<>();
        baseNodeList.forEach(item -> {
            CreoClassNode clsNode = createCreoClassNode(item);
            result.add(clsNode);
            buildRootInfoList(item,clsNode,prMap,waitQueryList);
        });
        queryAndSetProEnumerate(waitQueryList);
        return result;
    }

    private void queryAndSetProEnumerate(Map<String, List<CreoPropertyNode>> waitQueryList) {
        Map<String, List<DataDictionary>> parentCodeDataDictionaryListMap =
                jwiCommonService.dynamicQuery(DataDictionary.TYPE,
                        Condition.where(
                                "parentCode").in(waitQueryList.keySet()), DataDictionary.class).stream().collect(Collectors.groupingBy(DataDictionary::getParentCode));
        for (Map.Entry<String, List<CreoPropertyNode>> entry : waitQueryList.entrySet()) {
            if(parentCodeDataDictionaryListMap.containsKey(entry.getKey())) {
                List<DataDictionary> valueList = parentCodeDataDictionaryListMap.get(entry.getKey());
                for (CreoPropertyNode creoPropertyNode : entry.getValue()) {
                    creoPropertyNode.setEnumerate(dataDictionaryList2Map(valueList));
                }
            }
        }
    }

    private List<Map<String, String>> dataDictionaryList2Map(List<DataDictionary> valueList) {
        return valueList.stream().map(item -> {
            Map<String, String> map = new HashMap<>();
            map.put(KEY_TXT, item.getDisplayName());
            map.put(KEY_VALUE, item.getCode());
            return map;
        }).collect(Collectors.toList());
    }

    private void buildRootInfoList(ClassificationTreeNode baseNode, CreoClassNode parentInfoNode, Map<String, List<ClsPropertyWithRel>> prMap, Map<String, List<CreoPropertyNode>> waitQueryList) {
        if (CollectionUtils.isNotEmpty(baseNode.getChildren())) {
            baseNode.getChildren().forEach(child -> {
                CreoClassNode clsNode = createCreoClassNode(child);
                List<ClsPropertyWithRel> proList = prMap.get(child.getOid());
                if(ObjectUtils.isNotEmpty(proList)) {
                    proList.forEach(item-> clsNode.addChildren(createCreoProperty(item,waitQueryList)));
                }
                parentInfoNode.addChildren(clsNode);
                buildRootInfoList(child, clsNode, prMap, waitQueryList);
            });
        }
    }
    private CreoClassNode createCreoClassNode(ClassificationTreeNode classificationTreeNode) {
        CreoClassNode creoClassNode = new CreoClassNode();
        creoClassNode.setType(Classification.TYPE);
        creoClassNode.setCode(classificationTreeNode.getCode());
        creoClassNode.setName(classificationTreeNode.getDisplayName());
        creoClassNode.setClsOid(classificationTreeNode.getOid());
        return creoClassNode;
    }

    private CreoPropertyNode createCreoProperty(ClsPropertyWithRel clsPropertyWithRel,Map<String, List<CreoPropertyNode>> waitQueryList) {
        CreoPropertyNode creoPropertyNode = new CreoPropertyNode();
        creoPropertyNode.setType("Property");
        creoPropertyNode.setCode(clsPropertyWithRel.getCode());
        creoPropertyNode.setName(clsPropertyWithRel.getDisplayName());
        PropertyAssign propertyAssign = clsPropertyWithRel.getRelationship();
        creoPropertyNode.setRequired(propertyAssign.isRequired());
        if("enumtype".equalsIgnoreCase(propertyAssign.getConstraintType())) {
            JSONObject context = propertyAssign.getConstraintContext();
            String value = context.getString("valueEnum");
            if(StringUtils.isNotBlank(value)) {
                String enumValues[] = value.split(",");
                if(enumValues.length == 1) {
                    String enumValuesTmp[] = value.split("，");
                    if(enumValuesTmp.length > 1) {
                        enumValues = enumValuesTmp;
                    }
                }
                for (String enumValue : enumValues) {
                    Map<String,String> map = new HashMap<>();
                    map.put(KEY_TXT,enumValue);
                    map.put(KEY_VALUE,enumValue);
                    creoPropertyNode.addEnumerate(map);
                }
            }
        } else if(DataDictionary.TYPE.equalsIgnoreCase(propertyAssign.getConstraintType())) {
            JSONObject context = propertyAssign.getConstraintContext();
            String code = context.getString(DataDictionary.TYPE.toLowerCase());
            waitQueryList.computeIfAbsent(code, key -> Lists.newArrayList()).add(creoPropertyNode);
        }
        return creoPropertyNode;
    }


    private void getClsOidSet(List<ClassificationTreeNode> clasList,Set<String> oidSet) {
        clasList.forEach(item->{
            oidSet.add(item.getOid());
            if(CollectionUtils.isNotEmpty(item.getChildren())) {
                getClsOidSet(item.getChildren(),oidSet);
            }
        });
    }

    private static final Set<String> FIELD_SET = new HashSet<String>() {{
        //标准号
        add("cn_jwis_bzh");
        //生产厂家
        add("cn_jwis_sccj");
        //湿敏等级
        add("cn_jwis_smdj");
        //质量等级
        add("cn_jwis_zldj");
        //封装形式
        add("cn_jwis_fzxs");
    }};

    public Long updateClassLayoutMode(String code) {
        Set<String> codeSet = new HashSet<>();
        List<ClassificationTreeNode> classificationTreeNodeList = getClassificationTreeNodes();
        if (StringUtils.isNotBlank(code) && !"ALL".equals(code)) {
            classificationTreeNodeList = classificationTreeNodeList.stream().filter(item ->
                    code.equals(item.getCode())).collect(Collectors.toList());
        }
        getClsCodeSet(classificationTreeNodeList,codeSet);
        List<ClsLayout> clsLayoutList = customerCommonRepo.queryLayoutByClassCode(codeSet);


        List<ClsLayout> needUpdateList = clsLayoutList.stream().filter(item-> {
            boolean isNeedUpdate = false;
            JSONObject content = item.getContent();
            if(content == null || !content.containsKey("layout")) {
                return false;
            }
            JSONArray layList = content.getJSONArray("layout");
            for(int i = 0;i <layList.size();i++) {
                JSONArray lay = (JSONArray) layList.get(i);
                for(int j = 0;j <lay.size();j++) {
                    JSONObject object = lay.getJSONObject(j);
                    if("dropDown".equals(object.getString("compType")) && FIELD_SET.contains(object.getString(
                            "fieldName"))) {
                        if(!object.containsKey("mode")) {
                            object.put("mode","combobox");
                            isNeedUpdate = true;
                        }
                    }
                }
            }
            if(!isNeedUpdate) {
                log.info("skip: {}",item.getOid());
            }
            return isNeedUpdate;
        }).collect(Collectors.toList());
        log.info("needUpdate layout size : {}", needUpdateList.size());
        for (ClsLayout clsLayout : needUpdateList) {
            jwiCommonService.update(clsLayout);
        }
        return Long.valueOf(needUpdateList.size());
    }


    private void getClsCodeSet(List<ClassificationTreeNode> clasList,Set<String> codeSet) {
        clasList.forEach(item->{
            codeSet.add(item.getCode());
            if(CollectionUtils.isNotEmpty(item.getChildren())) {
                getClsCodeSet(item.getChildren(),codeSet);
            }
        });
    }
}
