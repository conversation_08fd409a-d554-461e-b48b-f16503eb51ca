package cn.jwis.product.pdm.customer.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import cn.jwis.product.pdm.customer.entity.DingTaskRecord;
import cn.jwis.product.pdm.customer.entity.assistant.CheckedAssist;
import cn.jwis.product.pdm.customer.entity.assistant.SafeWrapAssist;
import cn.jwis.product.pdm.customer.service.interf.PCNService;
import com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceHeaders;
import com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceRequest;
import com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceResponse;
import com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceResponseBody;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static sun.font.FontUtilities.isWindows;

@Slf4j
@Service
@Transactional
public class PCNServiceImpl implements PCNService, CheckedAssist, SafeWrapAssist {

    public final static String fileSeparator = java.io.File.separator;
    public final static String tmpPath = isWindows ? ("C:" + fileSeparator + "tmp" + fileSeparator) : (fileSeparator + "tmp" + fileSeparator);
    @Autowired
    DingTalkServiceImpl dingTalkServiceImpl;
    @Autowired
    SMISCall smisCall;

    @Override
    public void changeRelease(DingTaskRecord dingTaskRecord) throws Exception {
        String dingProcessInstanceId = dingTaskRecord.getDingProcessInstanceId();
        String tokenNew = dingTalkServiceImpl.getTokenNew();
        List<GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultFormComponentValues> dingFromValues = getDingFromValues(dingProcessInstanceId, tokenNew);

        // 处理物料清单数据
        String ecrOidStr = dingTaskRecord.getEcrOidList().get(0);

        Map<String, Object> formData = processFormData(dingFromValues, dingProcessInstanceId, ecrOidStr);

        // 调用SMIS接口
        String result = smisCall.postFormData(smisCall.getPcnPushUrl(), formData);
        log.info("PCN推送结果: {}", result);
    }

    public  List<GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultFormComponentValues> getDingFromValues(String processInstanceId, String token) throws Exception {
        Config config = new Config()
                .setProtocol("https")
                .setRegionId("central");
        com.aliyun.dingtalkworkflow_1_0.Client client = new com.aliyun.dingtalkworkflow_1_0.Client(config);

        GetProcessInstanceHeaders headers = new GetProcessInstanceHeaders()
                .setXAcsDingtalkAccessToken(token);

        GetProcessInstanceRequest request = new GetProcessInstanceRequest()
                .setProcessInstanceId(processInstanceId);

        GetProcessInstanceResponse response = client.getProcessInstanceWithOptions(request, headers, new RuntimeOptions());
        GetProcessInstanceResponseBody body = response.getBody();
        GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResult result = body.getResult();
        List<GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultFormComponentValues> formComponentValues = result.getFormComponentValues();
        return formComponentValues;
    }

    /**
     * 处理钉钉表单数据，构造SMIS接口所需的form-data
     */
    private Map<String, Object> processFormData(List<GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultFormComponentValues> formComponentValues,
                                               String processInstanceId, String businessId) throws Exception {
        Map<String, Object> formData = new HashMap<>();
        List<Map<String, Object>> items = new ArrayList<>();
        List<File> allFiles = new ArrayList<>();

        // 查找物料清单表单
        for (GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultFormComponentValues formComponentValue : formComponentValues) {
            String name = formComponentValue.getName();
            if (StrUtil.isNotEmpty(name) && "物料清单".equals(name)) {
                String wlmxJson = formComponentValue.getValue();
                log.info("物料清单Json: {}", wlmxJson);

                JSONArray jsonArray = JSONUtil.parseArray(wlmxJson);
                for (int i = 0; i < jsonArray.size(); i++) {
                    cn.hutool.json.JSONObject obj = jsonArray.getJSONObject(i);
                    cn.hutool.json.JSONArray rowValueArray = obj.getJSONArray("rowValue");

                    // 解析每行数据
                    Map<String, Object> item = processRowData(rowValueArray, processInstanceId, allFiles);
                    if (item != null) {
                        items.add(item);
                    }
                }
                break;
            }
        }

        // 构造data对象
        Map<String, Object> data = new HashMap<>();
        data.put("orderno", businessId);
        data.put("items", items);

        // 将data转换为JSON字符串并添加到formData
        formData.put("data", JSONUtil.toJsonStr(data));

        // 添加所有文件到formData
        for (int i = 0; i < allFiles.size(); i++) {
            File file = allFiles.get(i);
            formData.put("file" + (i + 1), file);
        }

        log.info("构造的formData: {}", JSONUtil.toJsonStr(formData));
        return formData;
    }

    /**
     * 处理表格中的每行数据
     */
    private Map<String, Object> processRowData(cn.hutool.json.JSONArray rowValueArray, String processInstanceId, List<File> allFiles) throws Exception {
        Map<String, Object> item = new HashMap<>();

        // 提取各个字段值
        String itemCode = getStringByKeyName(rowValueArray, "物料编码", "");
        String name = getStringByKeyName(rowValueArray, "名称", "");
        String specification = getStringByKeyName(rowValueArray, "规格", "");
        String workDefinition = getStringByKeyName(rowValueArray, "工艺路线版本", "");
        String affectedWorkRequests = getStringByKeyName(rowValueArray, "受影响工单", "");
        String affectedWorks = getStringByKeyName(rowValueArray, "受影响工序", "");
        String changeDetail = getStringByKeyName(rowValueArray, "工艺说明", "");
        String processFiles = getStringByKeyName(rowValueArray, "工艺规程", "[]");

        // 验证必要字段
        if (StrUtil.isEmpty(itemCode)) {
            log.warn("物料编码为空，跳过该行数据");
            return null;
        }

        // 处理工艺路线版本（逗号分隔）
        List<String> workDefinitions = new ArrayList<>();
        if (StrUtil.isNotEmpty(workDefinition)) {
            String[] parts = workDefinition.split(",");
            for (String part : parts) {
                String trimmed = StrUtil.trim(part);
                if (StrUtil.isNotEmpty(trimmed)) {
                    workDefinitions.add(trimmed);
                }
            }
        }

        // 处理受影响工单（逗号分隔）
        List<String> workRequestsList = new ArrayList<>();
        if (StrUtil.isNotEmpty(affectedWorkRequests)) {
            String[] parts = affectedWorkRequests.split(",");
            for (String part : parts) {
                String trimmed = StrUtil.trim(part);
                if (StrUtil.isNotEmpty(trimmed)) {
                    workRequestsList.add(trimmed);
                }
            }
        }

        // 处理受影响工序（逗号分隔）
        List<String> worksList = new ArrayList<>();
        if (StrUtil.isNotEmpty(affectedWorks)) {
            String[] parts = affectedWorks.split(",");
            for (String part : parts) {
                String trimmed = StrUtil.trim(part);
                if (StrUtil.isNotEmpty(trimmed)) {
//                    worksList.add(trimmed);
                    worksList.add(StrUtil.subBefore(trimmed, "-", false).trim());
                }
            }
        }

        // 处理工艺规程附件
        List<String> fileNames = processAttachments(processFiles, processInstanceId, allFiles);

        // 构造item对象
        item.put("itemcode", itemCode);
        item.put("workdefinition", workDefinitions.isEmpty() ? workDefinition : workDefinitions.get(0)); // 取第一个工艺路线版本
        item.put("workrequests", workRequestsList);
        item.put("works", worksList);
        item.put("changedetail", changeDetail);
        item.put("files", fileNames);

        log.info("处理行数据完成 - 物料编码: {}, 文件数量: {}", itemCode, fileNames.size());
        return item;
    }

    /**
     * 处理附件文件
     */
    private List<String> processAttachments(String processFilesJson, String processInstanceId, List<File> allFiles) throws Exception {
        List<String> fileNames = new ArrayList<>();

        if (StrUtil.isEmpty(processFilesJson) || "[]".equals(processFilesJson)) {
            return fileNames;
        }

        try {
            JSONArray fileJsonArray = JSONUtil.parseArray(processFilesJson);
            if (fileJsonArray != null && fileJsonArray.size() > 0) {
                for (int i = 0; i < fileJsonArray.size(); i++) {
                    cn.hutool.json.JSONObject fileObj = fileJsonArray.getJSONObject(i);
                    String fileId = fileObj.getStr("fileId");
                    String fileName = fileObj.getStr("fileName");

                    if (StrUtil.isNotEmpty(fileId) && StrUtil.isNotEmpty(fileName)) {
                        // 下载文件
                        File downloadedFile = downloadAttachment(processInstanceId, fileId, fileName);
                        if (downloadedFile != null) {
                            allFiles.add(downloadedFile);
                            // 使用文件索引作为key名称
                            String fileKey = "file" + (allFiles.size());
                            fileNames.add(fileKey);
                            log.info("成功处理附件: {} -> {}", fileName, fileKey);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理附件失败: {}", e.getMessage(), e);
            throw e;
        }

        return fileNames;
    }

    /**
     * 下载钉钉附件到本地临时文件
     */
    private File downloadAttachment(String processInstanceId, String fileId, String fileName) throws Exception {
        try {
            // 获取下载链接
            String downloadUri = dingTalkServiceImpl.getDINGFileDownloadUrl(processInstanceId, fileId);
            if (StrUtil.isEmpty(downloadUri)) {
                log.error("获取文件下载链接失败: fileId={}", fileId);
                return null;
            }

            // 构造本地文件路径
            String docFileTmpSavePath = tmpPath + fileName;
            File localFile = FileUtil.file(docFileTmpSavePath);

            // 下载文件
            HttpUtil.downloadFile(downloadUri, localFile);
            log.info("文件下载成功: {} -> {}", fileName, docFileTmpSavePath);

            return localFile;
        } catch (Exception e) {
            log.error("下载附件失败: fileName={}, fileId={}, error={}", fileName, fileId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 从表格行数据中根据字段名获取值
     */
    private static String getStringByKeyName(cn.hutool.json.JSONArray rowValueArray, String keyName, String defaultValue) {
        for (int k = 0; k < rowValueArray.size(); k++) {
            cn.hutool.json.JSONObject innerRowObj = rowValueArray.getJSONObject(k);
            if (keyName.equals(innerRowObj.getStr("label"))) {
                String value = innerRowObj.getStr("value");
                return StrUtil.isNotEmpty(value) ? value : defaultValue;
            }
        }
        return defaultValue;
    }
}
