package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.product.pdm.customer.entity.DingTaskRecord;
import cn.jwis.product.pdm.customer.entity.assistant.CheckedAssist;
import cn.jwis.product.pdm.customer.entity.assistant.SafeWrapAssist;
import cn.jwis.product.pdm.customer.service.interf.PCNService;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceHeaders;
import com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceRequest;
import com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceResponse;
import com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceResponseBody;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
@Transactional
public class PCNServiceImpl implements PCNService, CheckedAssist, SafeWrapAssist {


    @Autowired
    DingTalkServiceImpl dingTalkServiceImpl;
    @Autowired
    SMISCall smisCall;

    @Override
    public void changeRelease(DingTaskRecord dingTaskRecord) throws Exception {
        String dingProcessInstanceId = dingTaskRecord.getDingProcessInstanceId();
        String tokenNew = dingTalkServiceImpl.getTokenNew();
        List<GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultFormComponentValues> dingFromValues = getDingFromValues(dingProcessInstanceId, tokenNew);

        smisCall.pcnPush(new JSONObject());
    }

    public  List<GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultFormComponentValues> getDingFromValues(String processInstanceId, String token) throws Exception {
        Config config = new Config()
                .setProtocol("https")
                .setRegionId("central");
        com.aliyun.dingtalkworkflow_1_0.Client client = new com.aliyun.dingtalkworkflow_1_0.Client(config);

        GetProcessInstanceHeaders headers = new GetProcessInstanceHeaders()
                .setXAcsDingtalkAccessToken(token);

        GetProcessInstanceRequest request = new GetProcessInstanceRequest()
                .setProcessInstanceId(processInstanceId);

        GetProcessInstanceResponse response = client.getProcessInstanceWithOptions(request, headers, new RuntimeOptions());
        GetProcessInstanceResponseBody body = response.getBody();
        GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResult result = body.getResult();
        List<GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultFormComponentValues> formComponentValues = result.getFormComponentValues();
        return formComponentValues;
    }
}
