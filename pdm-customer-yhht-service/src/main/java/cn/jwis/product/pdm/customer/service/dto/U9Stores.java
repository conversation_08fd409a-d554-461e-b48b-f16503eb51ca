package cn.jwis.product.pdm.customer.service.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:U9库存
 * @date 2023/9/8 16:04
 * @Email <EMAIL>
 */
@Data
@EqualsAndHashCode
public class U9Stores {
    //物料编码
    private String itemCode;
    //组织编码
    private String OrgCode;
    //组织名称
    private String OrgName;
    //存储地点编码
    private String WhCode;
    //存储地点名称
    private String WhName;
    //数量
    private String Qty;

    //LotCode 批号
    private String LotCode;
    //BinCode 库位编码
    private String BinCode;
    //BinName 库位名称
    private String BinName;
}
