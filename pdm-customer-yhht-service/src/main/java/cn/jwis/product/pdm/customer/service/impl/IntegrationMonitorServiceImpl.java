package cn.jwis.product.pdm.customer.service.impl;

import cn.hutool.json.JSONUtil;
import cn.jwis.framework.base.domain.able.ModelAble;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.database.core.entity.EntityFilter;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityService;
import cn.jwis.platform.plm.workflow.engine.dto.ProOrdOfBizObjFuzzyPageDTO;
import cn.jwis.platform.plm.workflow.engine.dto.ProcessOrderDetail;
import cn.jwis.platform.plm.workflow.engine.service.interf.ProcessOrderHelper;
import cn.jwis.product.pdm.cad.ecad.entity.ECADIteration;
import cn.jwis.product.pdm.cad.mcad.entity.MCADIteration;
import cn.jwis.product.pdm.cad.mcad.param.Page;
import cn.jwis.product.pdm.change.entity.ECR;
import cn.jwis.product.pdm.customer.entity.DingTaskRecord;
import cn.jwis.product.pdm.customer.entity.IntegrationFeedback;
import cn.jwis.product.pdm.customer.entity.IntegrationRecord;
import cn.jwis.product.pdm.customer.repo.CustomerCommonRepo;
import cn.jwis.product.pdm.customer.repo.IntegrationMonitorRepo;
import cn.jwis.product.pdm.customer.service.dto.SendByNumDTO;
import cn.jwis.product.pdm.customer.service.dto.U9InventoryResponse;
import cn.jwis.product.pdm.customer.service.dto.U9ProductionOrder;
import cn.jwis.product.pdm.customer.service.interf.CommonService;
import cn.jwis.product.pdm.customer.service.interf.DingTalkService;
import cn.jwis.product.pdm.customer.service.interf.IntegrationMonitorService;
import cn.jwis.product.pdm.customer.service.release.EntityRelease;
import cn.jwis.product.pdm.customer.service.release.EntityReleaseFactory;
import cn.jwis.product.pdm.document.entity.DocumentIteration;
import cn.jwis.product.pdm.partbom.part.dto.SimplePartBOMNode;
import cn.jwis.product.pdm.partbom.part.entity.PartIteration;
import cn.jwis.product.pdm.partbom.part.service.PartHelper;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.RuntimeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/8/9 22:27
 * @Email <EMAIL>
 */
@Slf4j
@Service
@Transactional
public class IntegrationMonitorServiceImpl implements IntegrationMonitorService {

    @Autowired
    IntegrationMonitorRepo monitorRepo;

    @Autowired
    JWICommonService jwiCommonService;

    @Autowired
    RuntimeService runtimeService;

    @Autowired
    CommonAbilityService commonAbilityService;

    @Autowired
    CommonService commonService;

    @Autowired
    U9Call u9Call;

    @Autowired
    DingTalkService dingTalkService;

    @Autowired
    ProcessOrderHelper processOrderHelper;

    @Autowired
    PartHelper partHelper;
    @Autowired
    private CustomerCommonRepo customerCommonRepo;



    @Override
    public IntegrationRecord findByBusinessOid(String oid) {
        EntityFilter filter = new EntityFilter();
        filter.setType(IntegrationRecord.TYPE);
        filter.setFilter(Condition.where("businessOid").eq(oid));
        IntegrationRecord record = CollectionUtil.getFirst(jwiCommonService.dynamicQuery(filter,IntegrationRecord.class));
        return record;
    }

    @Override
    public List<IntegrationRecord> findRecord(Page page) {
        return monitorRepo.findRecord(page);
    }

    @Override
    public String reSend(IntegrationRecord record) {
        // 根据流程类型或者评审对象类型找到对应的发布能力提供者
        EntityRelease entityRelease = EntityReleaseFactory.match(record.getBusinessType());
        //真正发布
        entityRelease.release(null,record.getBizOid(),record.getOid(),0);
        return "";
    }

    @Override
    public String feedback(IntegrationFeedback dto,int count) throws InterruptedException {
        log.info("feedback.request:" + JSON.toJSONString(dto));
        EntityFilter filter = new EntityFilter();
        filter.setType(IntegrationRecord.TYPE);
        filter.setFilter(Condition.where("businessOid").eq(dto.getBusinessOid()));
//        IntegrationRecord record =  CollectionUtil.getFirst(jwiCommonService.dynamicQuery(filter,IntegrationRecord.class));
        Thread.sleep(2000);
        IntegrationRecord record =  jwiCommonService.dynamicQuery(filter,IntegrationRecord.class).stream().max(Comparator.comparing(it -> Long.valueOf(it.getCreateDate()))).orElse(null);
        if(record == null){
            if(count>3) {
                throw new JWIException("businessOid 不存在！");
            }else{
                Thread.sleep(2000);
                feedback(dto,++count);
            }
        }else{
            record.setMsg(dto.getMsg());
            record.setConsumer(dto.getConsumer());
            record.setIsSuccess(dto.getIsSuccess());
            String businessType = "";
            boolean isPCN = false;
            if (dto.getBusinessOid().contains("-")) {
                businessType = dto.getBusinessOid().split("_")[0];
            }else {
                businessType = dto.getBusinessOid();
                isPCN = true;
            }
            // 额外处理 "item" 类型的业务
            if ("item".equals(businessType)) {
                // 提取 partWithVersion (去掉 "item_" 前缀，去掉 "_" 连接符)
                String[] parts = dto.getBusinessOid().split("_");
                if (parts.length < 3) {
                    throw new IllegalArgumentException("Invalid businessOid format: " + dto.getBusinessOid());
                }
                String partWithVersion = parts[1] + parts[2]; // 直接拼接
                String bomBusinessOid = "bom_" + partWithVersion; // 拼接 BOM 业务 OID

                log.info("解析 BOM 业务 OID: businessOid={}, partWithVersion={}, bomBusinessOid={}",
                        dto.getBusinessOid(), partWithVersion, bomBusinessOid);

                log.info("需要额外校验BOM业务: {}", bomBusinessOid);

                // 查询 BOM 业务的 IntegrationRecord
                EntityFilter bomFilter = new EntityFilter();
                bomFilter.setType(IntegrationRecord.TYPE);
                bomFilter.setFilter(Condition.where("businessOid").eq(bomBusinessOid));

                IntegrationRecord bomRecord = jwiCommonService.dynamicQuery(bomFilter, IntegrationRecord.class)
                        .stream()
                        .max(Comparator.comparing(it -> Long.valueOf(it.getCreateDate())))
                        .orElse(null);

                // 校验 BOM 业务
                if (bomRecord != null && !Boolean.TRUE.equals(bomRecord.getIsSuccess())) {
                    log.warn("BOM 业务 {} 存在但未成功，终止状态更新！", bomBusinessOid);
                    return "BOM 业务未成功，状态更新终止";
                }
            }
            //  处理 "bom" 业务逻辑 (新需求)
            if ("bom".equals(businessType)) {
                log.info("当前业务类型为 BOM，检查是否成功");

                // 如果 BOM 业务发送成功，更新状态为 Released
                if (dto.getIsSuccess()) {
                    String lifeCycleStatus = "Released";
                    log.info("BOM 业务 {} 发送成功，更新状态为 {}", dto.getBusinessOid(), lifeCycleStatus);
                    monitorRepo.updateStatusByNumber(record.getBizNumber(), record.getBizType(), lifeCycleStatus);
                }
            }
            if (isPCN) {
                log.info("当前业务类型为 PCN，检查是否成功");
                if (dto.getIsSuccess()) {
                    String lifeCycleStatus = "Released";
                    log.info("PCN业务 {} 发送成功，更新状态为 {}", dto.getBusinessOid(), lifeCycleStatus);
                    customerCommonRepo.updateLifeStatus(ECR.TYPE, Collections.singletonList(record.getBizOid()), "Released");
                    record.setMsg("SMIS执行结果:" + dto.getMsg());
                }
            }

            if(dto.getIsSuccess() && EntityReleaseFactory.needUpdateStatusType.contains(businessType)) {
                boolean isInactive = dto.getBusinessOid().endsWith("_deactivated");
                String lifeCycleStatus = isInactive ? "Deactivated" : "Released";
                log.info("feedback record---->>>>{}", JSONUtil.toJsonStr(record));
                //在这里查询
                if ("Released".equals(lifeCycleStatus)) {
                    lifeCycleStatus = setStatusByBOM(record, lifeCycleStatus);
                }
                log.info("lifeCycleStatus最终结果---->>>>{}", lifeCycleStatus);
                monitorRepo.updateStatusByNumber(record.getBizNumber(), record.getBizType(), lifeCycleStatus);
//                if (record.getIsPartWithOutRM() == null || Boolean.FALSE.equals(record.getIsPartWithOutRM())) {
//                    monitorRepo.updateStatusByNumber(record.getBizNumber(), record.getBizType(), lifeCycleStatus);
//                }
            }
            if(!dto.getIsSuccess()){
//                dingTalkService.createNoticeTask(record.getBizNumber(),record.getBizType(),record.getOwner(),dto.getMsg());
                String message = String.format("[%s] %s", dto.getConsumer(), dto.getMsg());
                dingTalkService.createNoticeTask(record.getBizNumber(), record.getBizType(), record.getCreateBy(), message);
            }
            // 如果返回成功，处理流程变量
//            try {
//                dealProcessData(record);
//            }catch (Exception e){
//                try {
//                    Thread.sleep(300);
//                }catch (Exception e1){}
//                dealProcessData(record);
//            }
            commonAbilityService.updateEntity(record);
        }
        return "执行回调成功";
    }

    private String setStatusByBOM(IntegrationRecord record, String lifeCycleStatus) {
        String bizOid = record.getBizOid();
        ProOrdOfBizObjFuzzyPageDTO proOrdOfBizObjFuzzyPageDTO = new ProOrdOfBizObjFuzzyPageDTO();
        proOrdOfBizObjFuzzyPageDTO.setIndex(1);
        proOrdOfBizObjFuzzyPageDTO.setSize(1);
        proOrdOfBizObjFuzzyPageDTO.setBizOid(bizOid);
        proOrdOfBizObjFuzzyPageDTO.setBizType(PartIteration.TYPE);
        proOrdOfBizObjFuzzyPageDTO.setSearchKey("");

        PageResult<ProcessOrderDetail> processOrderDetailPageResult = processOrderHelper.fuzzyPageByBiz(proOrdOfBizObjFuzzyPageDTO);
        log.info("当前为物料 entityOid 对应历史: {}", bizOid);

        if (processOrderDetailPageResult != null && CollectionUtil.isNotEmpty(processOrderDetailPageResult.getRows())) {
            ProcessOrderDetail processOrderDetail = processOrderDetailPageResult.getRows().get(0);
            log.info("feedback.processOrderDetail 信息: name={}, bizOid={}", processOrderDetail.getName(), bizOid);

            if ("物料编码申请流程".equals(processOrderDetail.getName())) {
                try {
                    List<SimplePartBOMNode> bom = partHelper.findSimpleUseTree("", bizOid, 1);
                    log.info("当前为物料{}，对应bom: {}",record.getBizNumber(), JSONUtil.toJsonStr(bom));
                    if (CollectionUtil.isNotEmpty(bom) && CollectionUtil.isNotEmpty(bom.get(0).getChildren())) {
                        lifeCycleStatus = "Design";
                    }
                } catch (Exception e) {
                    log.error("Failed to find BOM for bizOid: {}", bizOid, e);
                }
                log.info("当前为物料编码申请流程，对应 bizOid: {}", bizOid);
            }
        }
        return lifeCycleStatus;
    }

    private void dealProcessData(IntegrationRecord record) {
        boolean isSuccess = record.getIsSuccess();
        if(!isSuccess){
            return; // 有处理失败的，则不更新流程变量
        }
        if(StringUtil.isBlank(record.getExecuteId())){
            return;
        }
        // 修改未发布的数据
        Object unReleaseEntityObj = runtimeService.getVariable(record.getExecuteId(), "unReleaseEntity");
        if(unReleaseEntityObj == null){
            log.info("IntegrationMonitorServiceImpl.dealProcessData.process was closed!" + record.getExecuteId());
            return;
        }
        String unReleaseEntity = unReleaseEntityObj.toString().replace(record.getBusinessOid() + ",","");
        runtimeService.setVariable(record.getExecuteId(), "unReleaseEntity",unReleaseEntity);
        // 记录已发布的数据
        Object releasedEntityObj = runtimeService.getVariable(record.getExecuteId(), "releasedEntity");
        if(releasedEntityObj == null){
            releasedEntityObj = "";
        }
        String releasedEntity = releasedEntityObj + record.getBusinessOid() + ",";
        runtimeService.setVariable(record.getExecuteId(), "releasedEntity",releasedEntity);
    }

    @Override
    public List<U9ProductionOrder> queryProductionOrderFromU9(String number) {
        List<U9ProductionOrder> result = new ArrayList<>();
        try {
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            JSONObject body = new JSONObject();
            body.put("itemCode", number);
            String responseStr = u9Call.send(u9Call.getWorkOrderUrl(),body);
            JSONObject responseJSON = JSONObject.parseObject(responseStr);
            JSONObject d = responseJSON.getJSONObject("d");
            String dataStr = d.getString("Data");
            if(StringUtil.isBlank(dataStr)){
                return result;
            }
            result.addAll(JSONArray.parseArray(dataStr, U9ProductionOrder.class));
        }catch (Exception e){
            log.error("queryProductionOrderFromU9 error:",e);
        }
        return result;
    }

    @Override
    public U9InventoryResponse queryInventoryFromU9(String numbers) {
        if(StringUtil.isBlank(numbers)){
            return null;
        }
        U9InventoryResponse allData = new U9InventoryResponse();
        try {
            for (String number : numbers.split(",")) {
                Map<String, String> headers = new HashMap<>();
                headers.put("Content-Type", "application/json");
                JSONObject body = new JSONObject();
                body.put("itemCode", number);
                String responseStr = u9Call.send(u9Call.getInventoryUrl(),body);
                JSONObject responseJSON = JSONObject.parseObject(responseStr);
                JSONObject d = responseJSON.getJSONObject("d");
                String dataStr = d.getString("Data");
                if(StringUtil.isBlank(dataStr)){
                    break;
                }
                U9InventoryResponse response = JSON.toJavaObject(JSON.parseObject(dataStr), U9InventoryResponse.class);
                if (CollectionUtil.isNotEmpty(response.getStores())) {
                    response.getStores().forEach(s -> s.setItemCode(number));
                }
                if (CollectionUtil.isNotEmpty(response.getTransit())) {
                    response.getTransit().forEach(t -> t.setItemCode(number));
                }
                allData.addAll(response);
            }
        }catch (Exception e){
            log.error("queryInventoryFromU9 error:",e);
        }
        return allData;
    }

    @Override
    public String queryIntegrationResult(String unReleaseEntity) {
        if(StringUtil.isBlank(unReleaseEntity)){
            return "";
        }
        StringBuffer result = new StringBuffer();
        for(String unReleased : unReleaseEntity.split(",")){
            IntegrationRecord record = findByBusinessOid(unReleased);
            if(record == null){
                continue;
            }
            StringBuffer unitMsg = new StringBuffer(record.getBizNumber()+"发布结果：");
            //unitMsg.append("MQ=").append(record.getMqSuccess()).append(",");
            unitMsg.append(record.getConsumer()).append("=").append(record.getIsSuccess());
            if(!record.getIsSuccess()) {
                unitMsg.append("(").append(record.getMsg()).append(")");
            }
            result.append(unitMsg).append("\r\n");
        }
        return result.toString();
    }

    @Override
    public String sendByNumber(SendByNumDTO dto) {
        String oid = checkData(dto);
        // 根据流程类型或者评审对象类型找到对应的发布能力提供者
        EntityRelease entityRelease = EntityReleaseFactory.match(dto.getType());
        //真正发布
        IntegrationRecord record = entityRelease.release(null,oid,null,dto.getIsFirst());
        Assert.notNull(record,"不满足发布条件！");
        return "success";
    }

    private String checkData(SendByNumDTO dto) {
        String entityType = "";
        switch (dto.getType()){
            case "item":
            case "bom":
            case "hzzBOM":
            case "techChange":
                entityType = PartIteration.TYPE;
                break;
            case "doc":
                entityType = DocumentIteration.TYPE;
                break;
            case "mcad":
                entityType = MCADIteration.TYPE;
                break;
            case "ecad":
                entityType = ECADIteration.TYPE;
                break;
            case "pcn":
                entityType = DingTaskRecord.TYPE;
                break;
        }
        Assert.notEmpty(entityType,"不确定编码的类型，请联系IT！");
        EntityFilter filter = new EntityFilter();
        filter.setType(entityType);

        // 对于pcn类型，使用businessId查找
        if ("pcn".equals(dto.getType())) {
            filter.setFilter(Condition.where("businessId").eq(dto.getNumber()));
        } else {
            filter.setFilter(Condition.where("number").eq(dto.getNumber()).and(Condition.where("latest").eq(true)));
        }

        ModelAble modelAble = CollectionUtil.getFirst(jwiCommonService.dynamicQuery(filter));
        Assert.notNull(modelAble,"编码不存在或编码跟发布类型不匹配，请检查！");
        //String status = ((LifecycleAble)modelAble).getLifecycleStatus();
        //Assert.isTrue("Released".equals(status),"状态未发布，不能推送！");
        return modelAble.getOid();
    }
}
