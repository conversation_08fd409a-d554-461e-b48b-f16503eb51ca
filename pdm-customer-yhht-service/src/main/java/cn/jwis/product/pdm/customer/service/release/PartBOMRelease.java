package cn.jwis.product.pdm.customer.service.release;

import cn.hutool.json.JSONUtil;
import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.platform.plm.account.repo.user.response.UserWithPositionAndOrg;
import cn.jwis.platform.plm.account.user.service.UserHelper;
import cn.jwis.platform.plm.account.user.service.UserService;
import cn.jwis.platform.plm.datadistribution.service.DataDistributionService;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.file.service.FileService;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityService;
import cn.jwis.platform.plm.sysconfig.entity.ConfigItem;
import cn.jwis.platform.plm.sysconfig.preferences.PreferencesService;
import cn.jwis.platform.plm.workflow.engine.order.entity.ProcessOrder;
import cn.jwis.product.pdm.cad.ecad.service.ECADService;
import cn.jwis.product.pdm.cad.mcad.service.MCADHelper;
import cn.jwis.product.pdm.cad.mcad.service.PartMcadService;
import cn.jwis.product.pdm.customer.entity.IntegrationRecord;
import cn.jwis.product.pdm.customer.service.dto.BOMReleaseDataDTO;
import cn.jwis.product.pdm.customer.service.dto.ChildPartDTO;
import cn.jwis.product.pdm.customer.service.impl.CustomerCommonServiceImpl;
import cn.jwis.product.pdm.partbom.part.dto.SimplePartBOMNode;
import cn.jwis.product.pdm.partbom.part.entity.PartIteration;
import cn.jwis.product.pdm.partbom.part.service.PartHelper;
import cn.jwis.product.pdm.partbom.part.service.PartService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import jodd.util.StringUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 部件BOM发布能力提供者
 * @date 2023/8/15 11:13
 * @Email <EMAIL>
 */
@Data
@EqualsAndHashCode
@Component
@Transactional
public class PartBOMRelease extends EntityRelease{

    private static final Logger logger = LoggerFactory.getLogger(PartBOMRelease.class);

    private final List<String> targetLifecycleStatus = Arrays.asList("Released","Deactivated","Design");

    @Autowired
    DataDistributionService dataDistributionService;

    @Autowired
    PartService partService;

    @Autowired
    PartHelper partHelper;

    @Autowired
    MCADHelper mcadHelper;

    @Autowired
    ECADService ecadService;

    @Autowired
    JWICommonService jwiCommonService;

    @Autowired
    CommonAbilityHelper commonAbilityHelper;

    @Autowired
    CommonAbilityService commonAbilityService;

    @Autowired
    CustomerCommonServiceImpl customerCommonServiceImpl;

    @Autowired
    PartMcadService partMcadService;

    @Autowired
    FileService fileService;

    @Autowired
    UserHelper userHelper;

    @Autowired
    PartEntityRelease partEntityRelease;

    @Resource
    PreferencesService preferencesService;

    @Autowired
    UserService userService;


    /**
     * 真正的Part发布实现，在此处推送发放的部件到任意外部接口，当前只发MQ。
     * @return
     */
    @Override
    @Async
    public IntegrationRecord release(ProcessOrder processOrder,String entityOid, String recordOid, int isFirst) {
        PartIteration dbPart = partHelper.findByOid(entityOid);
        if(dbPart == null) return null;

        if(!dbPart.isLatest()){
            PartIteration dbPartLatest = partHelper.findLatestByNumber(dbPart.getNumber(), dbPart.getViewOid());
            if(dbPartLatest != null)
                dbPart = partHelper.findByOid(dbPartLatest.getOid());
        }

        logger.info("PartBOMRelease.Release dbPart------->>>>>{}", JSONUtil.toJsonStr(dbPart));
        BOMReleaseDataDTO bomReleaseDataDTO = BeanUtil.copyProperties(dbPart,new BOMReleaseDataDTO());
        //只有 自制和委外加工件 对应的值为1和2 此时才发送U9
        String source = dbPart.getSource();
        if (source == null) {
            return null;
        }
        int sourceValue;
        switch (source) {
            case "委外加工件":
                sourceValue = 2;
                break;
            case "制造件":
                sourceValue = 1;
                break;
            default:
                logger.warn("当前物料来源: {}。该物料编码为: {},不发U9，只有制造件/委外加工件 才发", source, dbPart.getNumber());
                return null;
        }
        bomReleaseDataDTO.setSource(sourceValue);

        if(StringUtil.isBlank(bomReleaseDataDTO.getDescription())){
            bomReleaseDataDTO.setDescription("");
        }
        bomReleaseDataDTO.setBusinessOid(type + "_" + dbPart.getNumber() + "_" + dbPart.getVersion()); // 业务ID
        bomReleaseDataDTO.setProcessOrder(processOrder == null ? "" : processOrder.getNumber());
        bomReleaseDataDTO.setUpdateTime(dbPart.getUpdateDate());
        // 单位
        bomReleaseDataDTO.setDefaultUnit(partEntityRelease.findUnitsCode(bomReleaseDataDTO.getDefaultUnit()));
        // 发布人
        JSONObject extension = dbPart.getExtensionContent();
        String cn_jwis_xqr, publishUserAccount = "";
        if(extension != null && (cn_jwis_xqr = extension.getString("cn_jwis_xqr")) != null)
            if(cn_jwis_xqr.startsWith("{"))
                publishUserAccount = JSON.parseObject(cn_jwis_xqr).getString("account");
            else if(cn_jwis_xqr.startsWith("[") && cn_jwis_xqr.length() > 2)
                JSON.parseArray(cn_jwis_xqr).getJSONObject(0).getString("account");
        if("".equals(publishUserAccount))
            throw new JWIException("物料编码 [" + dbPart.getNumber() + "] 的编码需求人信息为空，请重新设置。");
        UserWithPositionAndOrg user = userHelper.findDetailByAccount(publishUserAccount);
        initPublisher(user,bomReleaseDataDTO);
        // BOM的料品形态 u9下游已不处理cn_jwis_form bon料品形态
//        initBOMForm(dbPart,bomReleaseDataDTO);
        // 状态
        initStatus(bomReleaseDataDTO);
        // 子项
        List<SimplePartBOMNode> bom = partHelper.findSimpleUseTree("",dbPart.getOid(),1);
        List<SimplePartBOMNode> children = bom.get(0).getChildren();
        if(CollectionUtil.isEmpty(children)){
            // 和U9沟通确定：没有子项就不发了，中间版本没有子项也不用发
            return null;
            // 只有自制类型采取进行字项添加 进行下发
        }else if (needChild(dbPart.getSource())){
            initBOM(children, bomReleaseDataDTO);
        }
        JSONObject releaseData = JSONObject.parseObject(JSONObject.toJSONString(bomReleaseDataDTO));

        logger.info("PartBOMRelease.releaseData------->>>>>>{}", JSONUtil.toJsonStr(releaseData));
        // 获取集成记录
        IntegrationRecord record = getIntegrationRecord(recordOid,type,processOrder,releaseData);

        if(processOrder != null && processOrder.getName().contains("物料停用流程")){
            if(StringUtils.isNotEmpty(record.getBizName()) && record.getData() != null){
                record.getData().fluentPut("name", "已失效-" + record.getData().getString("name"));
                record.setBizName("已失效-" + record.getBizName());
                if(record.getData().getJSONObject("extensionContent") != null)
                    if(StringUtils.isNotEmpty(record.getData().getJSONObject("extensionContent").getString("cn_jwis_gg")))
                        record.getData().getJSONObject("extensionContent").fluentPut("cn_jwis_gg", "已失效-" + record.getData().getJSONObject("extensionContent").getString("cn_jwis_gg"));
            }
        }

        // 发MQ
        String result = releaseMQ(record,dataDistributionService);
        record.setMqSuccess(StringUtil.isBlank(result) ? true : false);
        // U9处理结果，预设为 waiting
        record.setConsumer("u9");
        record.setIsSuccess(false);
        record.setMsg("waiting");
        return jwiCommonService.update(record);
    }

    // 默认采购件 Bom下发时需要子项
    private static final String DEFAULT_NEED_CHILD_SOURCE = "采购件";

    private boolean needChild(String source) {
        ConfigItem item = preferencesService.queryConfigValue("filterChildSource");
        List<String> filterList;
        if (item != null && StringUtils.isNotBlank(item.getValue())) {
            String[] split = item.getValue().split("\\|");
            filterList = Arrays.asList(split);
        } else {
            filterList = new ArrayList<String>() {{add(DEFAULT_NEED_CHILD_SOURCE);}};
        }
        for (String s : filterList) {
            if(StringUtils.equalsIgnoreCase(s,source)) {
                return false;
            }
        }
        return true;
    }
    private void initPublisher(UserWithPositionAndOrg user, BOMReleaseDataDTO partReleaseData) {
        if(user == null){
            partReleaseData.setUpdatorAccount("sys_admin");
            partReleaseData.setUpdatorNameAndDepartment("PDM系统管理员");
            return;
        }
        partReleaseData.setUpdatorAccount(user.getNumber());
        List<String> orgList = userService.getOrgTreeNameByAccount(user.getAccount());
        StringBuffer orgTree = new StringBuffer(user.getName() + ",");
        String companyName = orgList.get(orgList.size()-1);
        for(int i = orgList.size()-2; i > -1 ; i--){
            // 如果owner在多个部门中，只取第一个
            if(companyName.equals(orgList.get(i))){
                break;
            }
            orgTree.append(orgList.get(i));
            if(i > 0){
                orgTree.append("-");
            }
        }
        String result = orgTree.toString();
        if(result.endsWith("-")){
            result = result.substring(0,result.length()-1);
        }
        partReleaseData.setUpdatorNameAndDepartment(result);
    }

    //1自制，2委外
//    private void initBOMForm(PartIteration dbPart, BOMReleaseDataDTO bomReleaseDataDTO) {
//        JSONObject object = dbPart.getExtensionContent();
//        if(object.containsKey("cn_jwis_form")){
//            String formType = object.getString("cn_jwis_form");
//            int type = Arrays.asList("自制","1").contains(formType) ? 1 : 2;
//            bomReleaseDataDTO.setSource(type);
//        }
//    }

    /**
     * create=1,update=2
     * @param partReleaseData
     */
    private void initStatus(BOMReleaseDataDTO partReleaseData) {
        if("A01".equals(partReleaseData.getVersion()) || "A".equals(partReleaseData.getVersion())){
            String number = partReleaseData.getNumber();
            IntegrationRecord integrationRecord = jwiCommonService.dynamicQueryOne(IntegrationRecord.TYPE,
                    Condition.where("bizNumber").eq(number).
                            and(Condition.where("isSuccess").eq(Boolean.TRUE)), IntegrationRecord.class);
            logger.info("当前entity发布记录 integrationRecord---->>>{}", JSONUtil.toJsonStr(integrationRecord));
            partReleaseData.setLifecycleStatus(integrationRecord != null ? 2 : 1);
        }else{
            partReleaseData.setLifecycleStatus(2);
        }
        logger.info("PartBOMRelease.initStatus partReleaseData---->>>{}", JSONUtil.toJsonStr(partReleaseData));
    }

    /*public void initBOM(List<SimplePartBOMNode> children,BOMReleaseDataDTO partReleaseData) {
        int lineNumber = 10;
        List<ChildPartDTO> childPartDTOS = new ArrayList<>();
        for(SimplePartBOMNode node : children){
            ChildPartDTO childPartDTO = new ChildPartDTO();
            childPartDTO.setNumber(node.getNumber());
            childPartDTO.setQuantity(node.getUse().getQuantity());
            JSONObject useData = node.getUse().getExtensionContent();
            if(useData != null) {
                childPartDTO.setPosition(useData.getString("position"));
            }
            childPartDTO.setLineNumber(lineNumber);
            childPartDTOS.add(childPartDTO);
            lineNumber = lineNumber + 10;
        }
        partReleaseData.setChildren(childPartDTOS);
    }*/

    public void initBOM(List<SimplePartBOMNode> children, BOMReleaseDataDTO partReleaseData) {
        List<ChildPartDTO> childPartDTOS = new ArrayList<>();
        List<SimplePartBOMNode> withLineNumber = new ArrayList<>();
        List<SimplePartBOMNode> withoutLineNumber = new ArrayList<>();

        // 根据是否有 lineNumber 分组
        for (SimplePartBOMNode node : children) {
            JSONObject useData = node.getUse().getExtensionContent();
            if (useData != null && useData.getInteger("lineNumber") != null) {
                withLineNumber.add(node);
            } else {
                withoutLineNumber.add(node);
            }
        }

        // 有lineNumber的按行号排序
        withLineNumber.sort(Comparator.comparingInt(node -> {
            JSONObject useData = node.getUse().getExtensionContent();
            return useData.getIntValue("lineNumber");
        }));

        int maxLineNumber = 0;

        // 先处理有行号的
        for (SimplePartBOMNode node : withLineNumber) {
            ChildPartDTO childPartDTO = new ChildPartDTO();
            childPartDTO.setNumber(node.getNumber());
            childPartDTO.setQuantity(node.getUse().getQuantity());
            JSONObject useData = node.getUse().getExtensionContent();
            if (useData != null) {
//                childPartDTO.setPosition(useData.getString("position"));
                Integer lineNumber = useData.getInteger("lineNumber");
                if (lineNumber != null) {
                    childPartDTO.setLineNumber(lineNumber);
                    maxLineNumber = Math.max(maxLineNumber, lineNumber); // 更新最大行号
                }
            }
            childPartDTOS.add(childPartDTO);
        }

        // 再处理没有行号的，从最大行号+10开始
        int nextLineNumber = (maxLineNumber == 0 ? 0 : maxLineNumber) + 10;
        for (SimplePartBOMNode node : withoutLineNumber) {
            ChildPartDTO childPartDTO = new ChildPartDTO();
            childPartDTO.setNumber(node.getNumber());
            childPartDTO.setQuantity(node.getUse().getQuantity());
            JSONObject useData = node.getUse().getExtensionContent();
            if (useData != null) {
//                childPartDTO.setPosition(useData.getString("position"));
            }
            childPartDTO.setLineNumber(nextLineNumber);
            nextLineNumber += 10;
            childPartDTOS.add(childPartDTO);
        }

        partReleaseData.setChildren(childPartDTOS);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        type = EntityReleaseFactory.BusinessType.bom.name();
        EntityReleaseFactory.register(type,this);
    }
}
