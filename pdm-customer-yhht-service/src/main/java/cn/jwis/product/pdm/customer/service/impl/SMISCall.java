package cn.jwis.product.pdm.customer.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import cn.jwis.product.pdm.change.service.ChangeService;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;


@Service
@Slf4j
@Data
@EqualsAndHashCode
public class SMISCall {

    @Value("${smis.findWorkRequests.url}")
    private String findWorkRequestsUrl;

    @Value("${smis.findWorks.url}")
    private String findWorksUrl;

    @Value("${smis.pcnPush.url}")
    private String pcnPushUrl;
    @Autowired
    ChangeService changeService;


    private Map<String, String> getSMISHeader() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        return headers;
    }

    /**
     * POST JSON 请求
     */
    public String postJson(String url, JSONObject body) {
        log.info("SMISCall.postJson.body={}", body.toJSONString());
        HttpResponse response = HttpRequest.post(url)
                .addHeaders(getSMISHeader())
                .body(body.toJSONString())
                .execute();
        String result = response.body();
        log.info("SMISCall.postJson.response={}", result);
        return result;
    }

    /**
     * GET 请求 (带 query 参数)
     */
    public String get(String url, Map<String, Object> queryParams) {
        log.info("SMISCall.get.url={}, params={}", url, queryParams);
        HttpResponse response = HttpRequest.get(url)
                .addHeaders(getSMISHeader())
                .form(queryParams)
                .execute();
        String result = response.body();
        log.info("SMISCall.get.response={}", result);
        return result;
    }

    /**
     * POST form-data 请求
     */
    public String postFormData(String url, Map<String, Object> formData) {
        log.info("SMISCall.postFormData.url={}", url);
        log.info("SMISCall.postFormData={}", JSONUtil.toJsonStr(formData));
        HttpResponse response = HttpRequest.post(url)
                .form(formData)
                .execute();
        String result = response.body();
        log.info("SMISCall.postFormData.response={}", result);
        return result;
    }


    // ----------------- 三个业务方法 -----------------

    /**
     * 1. 工单查询
     */
    public String findWorkRequests(String itemCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("itemcode", itemCode);
        return get(findWorkRequestsUrl, params);
    }

    /**
     * 2. 工作查询
     */
    public String findWorks(String itemCode, String workDefinition) {
        Map<String, Object> params = new HashMap<>();
        params.put("itemcode", itemCode);
        params.put("workdefinition", workDefinition);
        return get(findWorksUrl, params);
    }

    /**
     * 3. 变更内容下发
     */
    public String pcnPush(JSONObject body) {
        // Hutool 的 form-data 必须用 Map
        return postFormData(pcnPushUrl, body.getInnerMap());
    }

    /**
     * 获取PCN推送URL
     */
    public String getPcnPushUrl() {
        return pcnPushUrl;
    }
}
