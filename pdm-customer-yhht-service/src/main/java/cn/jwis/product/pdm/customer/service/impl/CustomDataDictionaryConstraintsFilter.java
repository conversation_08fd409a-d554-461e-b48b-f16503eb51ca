package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.platform.plm.foundation.pipeline.manager.constraints.ConstraintsFilter;
import cn.jwis.platform.plm.foundation.pipeline.manager.constraints.DataDictionaryConstraintsFilter;
import cn.jwis.platform.plm.foundation.relationship.PropertyAssign;
import cn.jwis.product.pdm.customer.service.dto.LayoutSelectDTO;
import cn.jwis.product.pdm.customer.service.interf.DataDictionaryService;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Component
@Primary
public class CustomDataDictionaryConstraintsFilter extends DataDictionaryConstraintsFilter {

    @Resource
    private DataDictionaryService dataDictionaryService;

    public CustomDataDictionaryConstraintsFilter() {
        //  注册前先清除父类注册的实例
        removeOldFilter();
        register(this);
    }

    private void removeOldFilter() {
        try {
            Field field = ConstraintsFilter.class.getDeclaredField("registerFilters");
            field.setAccessible(true);
            List<ConstraintsFilter> filters = (List<ConstraintsFilter>) field.get(null);

            filters.removeIf(existing ->
                    existing.getClass() == DataDictionaryConstraintsFilter.class
            );
        } catch (Exception e) {
            throw new RuntimeException("无法移除旧的 DataDictionaryConstraintsFilter 注册", e);
        }
    }

    @Override
    protected String doFilter(String key, String displayName, Object value, PropertyAssign relationship) {
        if (Objects.equals(relationship.getConstraintType(), "datadictionary")
                && relationship.getConstraintContext() != null
                && StringUtil.isNotBlank(relationship.getConstraintContext().getString("datadictionary"))
                && value != null
                && StringUtil.isNotBlank(value.toString())) {

            String dicCode = relationship.getConstraintContext().getString("datadictionary");
            List<LayoutSelectDTO> selectList = dataDictionaryService.getEnableList(dicCode);

            Optional<LayoutSelectDTO> matched = selectList.stream()
                    .filter(item -> Objects.equals(item.getVal(), String.valueOf(value)))
                    .findFirst();

            if (!matched.isPresent()) {
                String display = StringUtil.isNotBlank(displayName) ? displayName : key;
                return String.format("值 [%s] 不存在，只允许选择已存在的 [%s]\r\n", value, display);
            }
        }

        return "";
    }
}