package cn.jwis.product.pdm.customer.service.delegate;

import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.util.SpringContextUtil;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.platform.plm.workflow.engine.order.entity.ProcessOrder;
import cn.jwis.platform.plm.workflow.engine.service.interf.ProcessOrderHelper;
import cn.jwis.product.pdm.change.entity.ECA;
import cn.jwis.product.pdm.change.response.ChangeInfo;
import cn.jwis.product.pdm.change.service.ECAServiceImpl;
import cn.jwis.product.pdm.customer.service.release.DocReleaseIDS;
import cn.jwis.product.pdm.document.entity.DocumentIteration;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.JavaDelegate;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 工作流执行代理：文档发布后通知IDS
 * @date 2023/8/15 10:44
 * @Email <EMAIL>
 */
@Slf4j
public class ReleaseIDSDelegate implements JavaDelegate {

    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        log.info("ReleaseIDSDelegate execute procId={}", processInstanceId);
        RuntimeService runtimeService = SpringContextUtil.getBean(RuntimeService.class);
        try {
            ProcessOrderHelper processOrderHelper = (ProcessOrderHelper) SpringContextUtil.getBean("processOrderHelperImpl");
            ECAServiceImpl ecaService = (ECAServiceImpl)SpringContextUtil.getBean("ECAServiceImpl");
            // processOrder：流程申请单对象
            ProcessOrder processOrder = processOrderHelper.findByProcessInstanceId(processInstanceId);
            log.info("ReleaseIDSDelegate  processOrder==>{}", JSONObject.toJSONString(processOrder));
            // instanceEntityList：评审对象集合（部件，文档等）
            List<InstanceEntity> originInstanceEntityList = processOrderHelper.findBizObject(processOrder.getOid());
            List<InstanceEntity> instanceEntityList = new ArrayList<>();
            for(InstanceEntity instanceEntity : originInstanceEntityList){
                if(ECA.TYPE.equals(instanceEntity.getType())){
                    List<ChangeInfo> list = ecaService.findChangeInfo(instanceEntity.getOid(),new ArrayList<>());
                    list.stream().forEach(changeInfo -> {
                        instanceEntityList.add(BeanUtil.copyProperties(changeInfo,new InstanceEntity()));
                    });
                }else {
                    instanceEntityList.add(instanceEntity);
                }
            }
            // 评审对象为空则不处理
            if(CollectionUtil.isEmpty(instanceEntityList)){
                log.info("ReleaseIDSDelegate  instanceEntity is empty! ");
                return;
            }
            // 发放数据,返回结果为此次需要发布的数据
//            releaseIDS(instanceEntityList);
        } catch (Exception e) {
            log.error("ReleaseIDSDelegate execute error==>", e);
            StringBuffer buffer = new StringBuffer();
            StackTraceElement[] stackTrace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : stackTrace) {
                buffer.append(stackTraceElement.toString() + "\n");
            }
            runtimeService.setVariable(execution.getId(), "releaseIDSError", "处理集成数据异常，请联系IT！\r\n" + buffer);
        }
    }

    private void releaseIDS(List<InstanceEntity> instanceEntityList) {
        DocReleaseIDS docReleaseIDS = (DocReleaseIDS) SpringContextUtil.getBean("docReleaseIDS");
        for(InstanceEntity entity : instanceEntityList){
            if(DocumentIteration.TYPE.equals(entity.getType())){
                docReleaseIDS.sendIDS(entity.getOid());
            }
        }
    }

}
