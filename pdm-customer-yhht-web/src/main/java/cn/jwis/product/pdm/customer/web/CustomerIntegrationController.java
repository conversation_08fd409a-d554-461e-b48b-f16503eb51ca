package cn.jwis.product.pdm.customer.web;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.iam.gateway.access.annotation.IgnoreRestUrlAccess;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.product.pdm.cad.mcad.param.Page;
import cn.jwis.product.pdm.change.response.ChangeInfo;
import cn.jwis.product.pdm.change.service.ChangeService;
import cn.jwis.product.pdm.customer.entity.DeliveryDocumentOperateDTO;
import cn.jwis.product.pdm.customer.entity.IntegrationFeedback;
import cn.jwis.product.pdm.customer.entity.IntegrationRecord;
import cn.jwis.product.pdm.customer.service.delegate.BatchSendERP;
import cn.jwis.product.pdm.customer.service.delegate.BatchSendHZZ;
import cn.jwis.product.pdm.customer.service.dto.SendByNumDTO;
import cn.jwis.product.pdm.customer.service.dto.SMISWorkdefinitionResponse;
import cn.jwis.product.pdm.customer.service.dto.SMISWorksDataResponse;
import cn.jwis.product.pdm.customer.service.impl.SMISCall;
import cn.jwis.product.pdm.customer.service.interf.IntegrationMonitorService;
import cn.jwis.product.pdm.customer.service.release.DocEntityRelease;
import cn.jwis.product.pdm.customer.service.release.PMSRelease;
import cn.jwis.product.pdm.customer.service.release.PartEntityRelease;
import cn.jwis.product.pdm.customer.service.release.TCOEntityRelease;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/8/9 22:22
 * @Email <EMAIL>
 */
@RestController
@RequestMapping(value = "/integration/")
@Api(tags = "pdm集成控制", value = "U9等集成发布以及监控", description = "integration")
@Slf4j
public class CustomerIntegrationController {

    @Autowired
    IntegrationMonitorService monitorService;

    @Autowired
    PartEntityRelease partEntityRelease;

    @Autowired
    DocEntityRelease docEntityRelease;

    @Autowired
    TCOEntityRelease tcoEntityRelease;

    @Autowired
    private SMISCall smisCall;
    @Autowired
    ChangeService changeService;


    @RequestMapping(value = "monitor/findByBusinessOid", method = RequestMethod.GET)
    @ApiOperation(response = Result.class, value = "")
    public Result findByBusinessOid(@RequestParam("businessOid") String businessOid) {
        return Result.success(monitorService.findByBusinessOid(businessOid));
    }

    @RequestMapping(value = "monitor/findRecord", method = RequestMethod.POST)
    @ApiOperation(value = "查询集成记录", notes = "查询集成记录")
    public Result findRecord(@RequestBody Page page) {
        List<IntegrationRecord> list = monitorService.findRecord(page);
        return Result.pagination(page.getCount(), page.getIndex(), page.getSize(),list);
    }

    @RequestMapping(value = "monitor/reSend", method = RequestMethod.POST)
    @ApiOperation(value = "重发数据到MQ", notes = "重发数据到MQ，重置此条消息的状态")
    public Result reSend(@RequestBody IntegrationRecord record) {
        String s =  monitorService.reSend(record);
        return Result.success(s);
    }

    @RequestMapping(value = "monitor/sendByNumber", method = RequestMethod.POST)
    @ApiOperation(value = "根据编码推送下游", notes = "根据编码推送下游")
    public Result sendByNumber(@RequestBody SendByNumDTO dto) {
        String s =  monitorService.sendByNumber(dto);
        return Result.success(s);
    }

    @RequestMapping(value = "monitor/feedback", method = RequestMethod.POST)
    @ApiOperation(value = "接收U9等下游的集成消费结果", notes = "接收U9等下游的集成消费结果")
    public Result feedback(@RequestBody IntegrationFeedback dto) throws Exception {
        String s =  monitorService.feedback(dto, 0);
        return Result.success(s);
    }

    @RequestMapping(value = "queryInventoryFromU9", method = RequestMethod.GET)
    @ApiOperation(response = Result.class, value = "U9-根据物料编码查询库存信息")
    public Result queryInventoryFromU9(@RequestParam("number") String numbers) {
        return Result.success(monitorService.queryInventoryFromU9(numbers));
    }
    @RequestMapping(value = "queryProductionOrderFromU9", method = RequestMethod.GET)
    @ApiOperation(response = Result.class, value = "U9-根据物料编码查询工单信息")
    public Result queryProductionOrderFromU9(@RequestParam("number") String number) {
        return Result.success(monitorService.queryProductionOrderFromU9(number));
    }

    @RequestMapping(value = "releasePart", method = RequestMethod.GET)
    @ApiOperation(response = Result.class, value = "发布部件/BOM/图档信息")
    public Result releasePart(@RequestParam("bizOid") String bizOid,
                              @RequestParam(value = "oid", required = false) String oid) {
        return Result.success(partEntityRelease.release(null,bizOid,oid,0));
    }

    @RequestMapping(value = "releaseDoc", method = RequestMethod.GET)
    @ApiOperation(response = Result.class, value = "发布文档数据")
    public Result releaseDoc(@RequestParam("bizOid") String entityOid,
                              @RequestParam(value = "oid", required = false) String recordOid) {
        return Result.success(docEntityRelease.release(null,entityOid,recordOid,0));
    }

    @RequestMapping(value = "releaseTCO", method = RequestMethod.GET)
    @ApiOperation(response = Result.class, value = "发布技术变更单")
    public Result releaseTCO(@RequestParam("bizOid") String entityOid,
                              @RequestParam(value = "oid", required = false) String recordOid) {
        return Result.success(tcoEntityRelease.release(null,entityOid,recordOid,0));
    }

    @RequestMapping(value = "queryIntegrationResult", method = RequestMethod.GET)
    @ApiOperation(response = Result.class, value = "根据businessOid查询集成异常信息")
    public Result queryIntegrationResult(@RequestParam(value = "unReleaseEntity", required = false) String unReleaseEntity) {
        return Result.success(monitorService.queryIntegrationResult(unReleaseEntity));
    }

    @Autowired
    private BatchSendERP batchSendERP;
    @Autowired
    private BatchSendHZZ batchSendHZZ;
    @Autowired
    private PMSRelease pmsRelease;

    @RequestMapping(value = "batchSendERP", method = RequestMethod.POST)
    @ApiOperation(value = "批量发送物料到ERP", notes = "批量发送物料到ERP")
    public Result batchSendERP(@RequestBody List<InstanceEntity> originInstanceEntityList) {
        return Result.success(batchSendERP.batchSendERP(originInstanceEntityList, Boolean.TRUE));
    }

    @RequestMapping(value = "batchSendHZZ", method = RequestMethod.POST)
    @ApiOperation(value = "批量发送物料到HZZ", notes = "批量发送物料到HZZ")
    public Result batchSendHZZ(@RequestBody List<InstanceEntity> originInstanceEntityList) {
        return Result.success(batchSendHZZ.batchSendHZZ(originInstanceEntityList, Boolean.TRUE));
    }


    @ApiOperation(value = "pms发布", notes = "根据文档oid发布预期文档到PMS")
    @PostMapping("/pmsRelease")
    @IgnoreRestUrlAccess
    public Result<String> pmsRelease(@RequestBody DeliveryDocumentOperateDTO deliveryDocumentOperateDTO) {
        try {
            pmsRelease.pushDocumentInfoToPMS(deliveryDocumentOperateDTO.getFileId(), deliveryDocumentOperateDTO.getFlag());
            return Result.success("推送成功");
        } catch (Exception e) {
            return Result.Fail("推送失败：" + e.getMessage());
        }
    }

   /* @ApiOperation(value = "工艺路线查询", notes = "根据物料编码查询SMIS工单")
    @GetMapping("/findWorkRequestsFromSMIS")
    @IgnoreRestUrlAccess
    public JSONObject findWorkRequests(@RequestParam String ecrOid) {
        if (ecrOid == null || ecrOid.trim().isEmpty()) {
            return JSON.parseObject("{\"code\":2,\"msg\":\"参数异常：ecrOid不能为空\"}");
        }

        List<ChangeInfo> changeInfoList = changeService.findChangeInfo(ecrOid, null);
        if (changeInfoList == null || changeInfoList.isEmpty()) {
            return JSON.parseObject("{\"code\":2,\"msg\":\"未找到对应的变更信息\"}");
        }

        ChangeInfo change = changeInfoList.get(0);
        InstanceEntity changeSchema = JSON.toJavaObject(
                changeService.findChangeScheme(change.getOid(), change.getType()),
                InstanceEntity.class
        );

        if (changeSchema == null || changeSchema.getNumber() == null) {
            return JSON.parseObject("{\"code\":2,\"msg\":\"未找到物料编码\"}");
        }

        String itemcode = changeSchema.getNumber();
        String smisResp = smisCall.findWorkRequests(itemcode);
        JSONObject jsonObject = JSON.parseObject(smisResp);
        jsonObject.put("itemcode", itemcode);

        return jsonObject;
    }*/
    @ApiOperation(value = "工艺路线查询", notes = "根据物料编码查询SMIS工单")
    @GetMapping("/findWorkRequestsFromSMIS")
    @IgnoreRestUrlAccess
    public Result findWorkRequests(@RequestParam String ecrOid) {
        List<ChangeInfo> changeInfoList = changeService.findChangeInfo(ecrOid, null);
        String itemcode = "";
        if (CollUtil.isNotEmpty(changeInfoList)) {
            ChangeInfo change = changeInfoList.get(0);
            InstanceEntity changeSchema = JSON.toJavaObject(
                    changeService.findChangeScheme(change.getOid(), change.getType()),
                    InstanceEntity.class
            );
            itemcode = changeSchema.getNumber();
        }

        // 调用工具类请求
        String respStr = smisCall.findWorkRequests(itemcode);
        cn.hutool.json.JSONObject jsonObj = JSONUtil.parseObj(respStr);
        cn.hutool.json.JSONObject data = jsonObj.getJSONObject("data");
        SMISWorkdefinitionResponse response = data.toBean(SMISWorkdefinitionResponse.class);
        // 手动补上 itemcode
        response.setItemcode(itemcode);

        return Result.success(response);
    }

    @ApiOperation(value = "工序查询", notes = "根据物料编码和工单查询SMIS工序")
    @GetMapping("/findWorksFromSMIS")
    @IgnoreRestUrlAccess
    public Result findWorks(@RequestParam String itemcode,
                            @RequestParam String workdefinition) {


        String respStr = smisCall.findWorks(itemcode, workdefinition);
        cn.hutool.json.JSONObject jsonObj = JSONUtil.parseObj(respStr);
        cn.hutool.json.JSONObject data = jsonObj.getJSONObject("data");
        SMISWorksDataResponse response = data.toBean(SMISWorksDataResponse.class);
        return Result.success(response);
    }

    @ApiOperation(value = "变更内容下发", notes = "下发变更内容至SMIS")
    @PostMapping("/pcnPushToSMIS")
    @IgnoreRestUrlAccess
    public Result pcnPush(@RequestBody JSONObject body) {
        return Result.success(smisCall.pcnPush(body));
    }



}
