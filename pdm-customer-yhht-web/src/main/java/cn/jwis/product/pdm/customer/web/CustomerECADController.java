package cn.jwis.product.pdm.customer.web;

import cn.jwis.framework.base.response.Result;
import cn.jwis.product.pdm.cad.ecad.service.ECADHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@RequestMapping({"/ecad"})
@Api(tags = {"ecad客制化"},value = "ecad客制化", description = "ecad客制化")
public class CustomerECADController {

    @Autowired
    private ECADHelper ecadHelper;

    @RequestMapping(
            value = {"/batchDeleteByMasterOid"},
            method = {RequestMethod.POST}
    )
    @ApiOperation(
            response = Result.class,
            value = "通过master 批量删除删除所有迭代"
    )
    public Result batchDeleteByMasterOid(@RequestBody List<String> masterOids) {
        return Result.success(this.ecadHelper.deleteByMasterOid(masterOids));
    }



}
