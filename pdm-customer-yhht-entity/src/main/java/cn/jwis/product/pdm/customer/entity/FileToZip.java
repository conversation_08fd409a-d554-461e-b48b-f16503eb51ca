package cn.jwis.product.pdm.customer.entity;

import lombok.Data;

/**
 * 用于统一压缩包的文件信息类
 */
@Data
public class FileToZip {
    private String fileName;        // 文件名
    private String fileOid;         // 文件OID
    private String instanceNumber;  // 所属实例编号
    private String instanceName;    // 所属实例名称
    private String fileType;        // 文件类型（primary/secondary/generated）
    private byte[] fileContent;     // 文件内容（如果已下载）
    private java.io.File tempFile;  // 临时文件（如果已下载到本地）

    public FileToZip(String fileName, String fileOid, String instanceNumber, String instanceName, String fileType) {
        this.fileName = fileName;
        this.fileOid = fileOid;
        this.instanceNumber = instanceNumber;
        this.instanceName = instanceName;
        this.fileType = fileType;
    }


    /**
     * 生成在压缩包中的文件路径
     * 格式：实例编号_实例名称/文件类型/文件名
     */
    public String getZipEntryPath() {
        String sanitizedInstanceNumber = sanitizeFileName(instanceNumber);
        String sanitizedInstanceName = sanitizeFileName(instanceName);
        String sanitizedFileName = sanitizeFileName(fileName);
//        return sanitizedInstanceNumber + "_" + sanitizedInstanceName + "/" + fileType + "/" + sanitizedFileName;
        return  sanitizedFileName;
    }

    /**
     * 清理文件名中的非法字符
     */
    private String sanitizeFileName(String fileName) {
        if (fileName == null) return "unknown";
        return fileName.replaceAll("[\\\\/:*?\"<>|]", "_");
    }
}