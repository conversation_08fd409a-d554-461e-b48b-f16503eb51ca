package cn.jwis.product.pdm.customer.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> yefei
 * @Date ： 2024/3/26
 * @Description :
 */

@Data
@EqualsAndHashCode
public class CreoClassNode implements CreoTreeNode {

    private String clsOid;

    private String name;

    private String code;

    private String type;

    private List<CreoTreeNode> children;

    @Override
    public void addChildren(CreoTreeNode child) {
        if(ObjectUtils.isEmpty(children)) {
            children = new ArrayList<>();
        }
        children.add(child);
    }

    @Override
    public void addEnumerate(Map<String, String> map) {
        // doNothing
    }
}
